<?php
/**
 *
 * User: 袁志凡
 * Date-Time: 2024/12/10 上午10:04
 */

namespace field_utils;

use app\infrastructure\model\FieldConfigModel;
use think\Collection;
use think\Exception;
use think\facade\Route;

class Transfer
{
    private int $moduleId;
    private $projectId;
    private array $fieldList;

    private $fieldTypeList
        = [
            'Casader',
            'ApiSelect',
            'Select',
        ];

    public function __construct($moduleId, $projectId = null)
    {
        $this->moduleId = $moduleId;
        $this->projectId = $projectId;
        $this->getFieldList();

    }


    private function getFieldList()
    {
        $fieldList = FieldConfigModel::findListByModuleIdAndProjectId($this->moduleId, $this->projectId);

        if ( ! $fieldList) {
            throw new Exception("未查询到组件");
        }

        $this->fieldList = array_merge(
            array_column($fieldList->toArray(), null, 'field_name'),
            array_column($fieldList->toArray(), null, 'field_label'),
        );
    }


    /**
     * 通过组件获取获取对应的值(用于下拉，级联类型组件的kv值转换)
     * @param $name  string 组件名称
     * @param $value mixed 值
     * @return mixed
     * @throws Exception
     * <AUTHOR>
     * @date   2024/12/10 下午5:17
     */
    public function parse($name, $value)
    {
        $field = $this->fieldList[$name] ?? false;
        if ( ! $field) {
            throw new Exception("不存在的组件");
        }

        if ( ! in_array($field['component_type'], $this->fieldTypeList)) {
            return $value;
        }

        $this->initField($field);

        if ($field['component_type'] == "Casader") {
            $value = implode('/', $value);
        }

//        if ($field['field_name'] == "type_id"){
//            dd($this->fieldList[$name]['data'],$value);
//        }

        if ( ! isset($this->fieldList[$name]['data'][$value])) {
//            return "{$this->fieldList[$name]['field_label']}：\"{$value}\"不存在";
            throw new Exception("{$this->fieldList[$name]['field_label']}：\"{$value}\"不存在");
        }

        return $this->fieldList[$name]['data'][$value];
    }


    private function initField($field)
    {

        if (isset($field['data'])) {
            return;
        }

        switch ($field['component_type']) {
        case 'Casader':
            $this->casader($field);
            break;
        case 'ApiSelect':
            $this->apiSelect($field);
            break;
        case 'Select':
            $this->select($field);
            break;
        default :
            throw new Exception("未知的组件类型");
        }
    }

    private function select($field)
    {
        $data = $field['field_component']['options'];
        $this->fieldList[$field['field_name']]['data'] = array_column($data, 'label', 'value');
        $this->fieldList[$field['field_label']]['data'] = array_column($data, 'value', 'label');

    }

    private function apiSelect($field)
    {
        $data = $this->getApiData($field['field_component']['url'], $field['field_component']['extra']);


        $value = $field['field_component']['valueKey'];
        $label = 'label';

        if ($field['field_component']['fields'] ?? false) {
            $label = $field['field_component']['fields'][0]['key'];
        }
        if ($field['field_component']['url']=='/devops/project/projectUser/selectorListQuery'){
            $label = 'user_name';
        }


        $this->fieldList[$field['field_name']]['data'] = array_column($data, $label, $value);
        $this->fieldList[$field['field_label']]['data'] = array_column($data, $value, $label);
    }

    private function casader($field)
    {
        $this->fieldList[$field['field_name']]['data'] = $this->flattenTreeWithPath($field['field_component']['options'], 'children', 'value', 'value', 'label');
        $this->fieldList[$field['field_label']]['data'] = $this->flattenTreeWithPath($field['field_component']['options'], 'children', 'value', 'label', 'value');
    }


    private function getApiData($url, $params)
    {

        $params = json_decode($params, true);

        $url = str_replace('/devops/', '', $url);
        $ruleList = Route::getRule($url);
        if ( ! $ruleList) {
            throw new Exception("api下拉组件url解析错误：没有匹配的路由");
        }
        $firstKey = array_key_first($ruleList);
        $s = explode('@', $ruleList[$firstKey]->getName());
        $controller = $s[0];
        $action = $s[1];


        if (array_key_exists('project_id', $params)) {
            $params['project_id'] = $this->projectId;
        }


        $request = request()->withGet($params)->withPost($params)->setRoute([]);

        $result = app($controller)->$action($request)->getData()['data'];

        if ($result instanceof Collection) {
            $result = $result->toArray();
        }

        return $result;
    }

    /**
     * 将树结构按指定字段平铺，并以路径作为结果的键
     *
     * @param  array   $tree           树形结构数据
     * @param  string  $childrenField  子节点字段名称
     * @param  string  $idField        节点的唯一标识字段
     * @param  string  $pathField      用于生成路径的字段名称
     * @param  string  $valueField     用于生成值路径的字段名称
     * @param  string  $separator      路径分隔符
     * @param  string  $currentPath    当前路径，递归时使用
     * @return array 平铺后的数组，键为路径
     */
    private function flattenTreeWithPath(
        array $tree,
        string $childrenField = 'children',
        string $idField = 'id',
        string $pathField = 'name',
        string $valueField = 'label',
        string $separator = '/',
        string $currentPath = ''
    ): array {
        $flattened = [];

        foreach ($tree as $node) {
            // 构造当前节点路径
            $nodePath = ltrim($currentPath.$separator.$node[$pathField], $separator);
            $valuePath = ltrim($currentPath.$separator.$node[$valueField], $separator);
            $value = explode($separator, $valuePath);
            // 提取当前节点信息，移除子节点
            $currentNode = $node;
            $currentNode['path'] = $nodePath; // 保存路径
            unset($currentNode[$childrenField]);

            // 使用路径作为键
            $flattened[$nodePath] = $value;

            // 如果有子节点，递归平铺
            if ( ! empty($node[$childrenField])) {
                $flattened += $this->flattenTreeWithPath(
                    $node[$childrenField],
                    $childrenField,
                    $idField,
                    $pathField,
                    $valueField,
                    $separator,
                    $nodePath
                );
            }
        }

        return $flattened;
    }


}