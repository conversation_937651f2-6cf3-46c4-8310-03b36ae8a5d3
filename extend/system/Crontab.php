<?php

namespace system;

use app\project\scheduled_tasks\BugStatistics;
use \React\EventLoop\Loop;
use utils\Log;

class Crontab
{
    private $timer = [];
    /** @var */
    private $loop;
    private $renewalTimer;

    public function __construct()
    {
        $this->loop = Loop::get();
    }

    public function exec()
    {
        $this->loop->addPeriodicTimer(30, function () {
            // echo "5S 执行选举，当前时间: " . date('Y-m-d H:i:s') . PHP_EOL;
            $leaderElection = new LeaderElection();
            $leaderElection->runElection();
            if ($leaderElection->isLeader()) {
                if (!$this->renewalTimer) {
                    $this->renewalTimer = $this->loop->addPeriodicTimer(30, function () use ($leaderElection) {
                        $leaderElection->renewLease();
                    });
                }
                if (empty($this->timer)) {
                    $this->startTask();
                }
            } else {
                if ($this->renewalTimer) {
                    $this->loop->cancelTimer($this->renewalTimer);
                    $this->renewalTimer = null; // 重置续期定时器
                    $this->clearTask();
                }

            }
        });
    }

    private function startTask()
    {
        $loop = $this->loop;
        // 每天 10 点执行任务
        $hour = 10;
        $minute = 0;

        // 指定每天几点
        //      $nextExecution = $this->getNextExecutionTime($hour, $minute);
//        $this->timer[] = $loop->addTimer($nextExecution, function () use ($loop) {
//            echo "Task executed at " . date('Y-m-d H:i:s') . PHP_EOL;
//
//            // 设置下次执行
//            $loop->addPeriodicTimer(86400, function () {
//                echo "Task executed at " . date('Y-m-d H:i:s') . PHP_EOL;
//            });
//        });

        // 3秒之后执行
//        $this->timer[] = $loop->addTimer(3, function () {
//
//            echo gethostname() . "异步等待 3 秒后执行此操作" . PHP_EOL;
//        });

        // 每秒执行一次
//        $this->timer[] = $loop->addPeriodicTimer(1, function () {
//            $redis = Cache::store('redis');
//            $redis->set("leader_test", gethostname() . "每隔 1 秒执行此操作");
//            //echo gethostname() . "每隔 1 秒执行此操作" . PHP_EOL;
//        });

        // 每半钟执行一次定时任务
        $this->timer[] = $loop->addPeriodicTimer(30, function () {
            BugStatistics::statistics();
            Log::instance(Log::PLATFORM)->info('每半分钟一次');
//            exec("nohup php think handle perMinuteExecute > /dev/null 2>&1 &");

        });

//        // 每天9点运行定时任务
//        $nextExecution = $this->getNextExecutionTime(9, $minute);
//        $this->timer[] = $loop->addTimer($nextExecution, function () use ($loop) {
//            // 设置下次执行
//            $this->timer[] = $loop->addPeriodicTimer(86400, function () {
////                Log::instance(Log::SCHEDULED)->info('每天9点运行定时任务');
//                exec("nohup php think handle everyNineClockExecute > /dev/null 2>&1 &");
//            });
////            Log::instance(Log::SCHEDULED)->info('每天9点运行定时任务');
//            exec("nohup php think handle everyNineClockExecute > /dev/null 2>&1 &");
//        });


        // 开始事件循环
        $loop->run();

    }


    private function clearTask()
    {
        foreach ($this->timer as $timer) {
            $this->loop->cancelTimer($timer); // 取消每个定时器
        }
        $this->timer = [];
    }

    private function getNextExecutionTime($hour, $minute)
    {
        $now = new \DateTime();
        $next = new \DateTime();
        $next->setTime($hour, $minute, 0); // 设置为目标时间

        // 如果目标时间已经过去，则设置为明天的目标时间
        if ($now > $next) {
            $next->modify('+1 day');
        }

        return $now->diff($next)->s + ($now->diff($next)->i * 60) + ($now->diff($next)->h * 3600);
    }


}
