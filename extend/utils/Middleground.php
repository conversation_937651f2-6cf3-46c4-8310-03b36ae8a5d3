<?php
declare(strict_types=1);

namespace utils;

use app\project\model\ProjectCategorySettingsModel;
use app\project\model\ProjectUserModel;
use app\work_items\model\WorkItemsModel;
use basic\BaseModel;
use exception\NotFoundException;
use GuzzleHttp\Client;
use think\facade\Log;

/**
 * 中台消息工具类
 *
 * <AUTHOR>
 * @date   2024/02/26
 */
class Middleground
{


    const USER = "/userApi";
    /**
     * HTTP 客户端
     * @var Client
     */
    private Client $client;

    /**
     * 构造函数
     */
    private function __construct()
    {
        $this->client = new Client([
            'timeout' => 5.0,
            'headers' => [
                'Content-Type' => 'application/json; charset=utf-8'
            ]
        ]);
    }

    static function instance(): self
    {
        return new self();
    }


    /**
     * 发送缺陷提醒消息
     * @param  int    $cnt_id     缺陷id
     * @param  int    $operator   操作人id
     * @param  array  $receivers  接受者id集合
     * @return array|mixed|string
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function defectNotificationMessage(int $cnt_id, int $operator, array $receivers)
    {
        $model = WorkItemsModel::findById($cnt_id);
        if ( ! $model) {
            throw new NotFoundException();
        }

        $project_id = (int)$model->extends['project_id'];
        $title = $model->extends['title'];

        $typeModel = ProjectCategorySettingsModel::findByCategoryId(BaseModel::SETTING_TYPE_DEFECT, $model->extends['type_id']);

        $operatorModel = ProjectUserModel::findUserById($operator);


        $link = "/project/defectDetail/{$project_id}?id={$cnt_id}&project_id={$project_id}&title={$title}";

        $receivers = array_map(function ($item) {
            return [
                "recipientType" => 1,
                'receiverId'    => $item,
            ];
        }, $receivers);

        return $this->SendMessageByTemplate([
            'system_id'     => env('platform.center_sub_system_id'),
            'msg_source'    => 1,
            'template_code' => 'notificationOfDefects',
            'channels'      => [1, 2],//飞书，站内信
            'variables'     => [
                [
                    'key'   => 'operator',//操作人
                    'value' => $operatorModel->user_name,
                ],
                [
                    'key'   => 'item',//工作项名称
                    'value' => "缺陷",
                ],
                [
                    'key'   => 'link',//跳转链接
                    'value' => "{$typeModel->category_name}-{$title} ".env('front_end_address.url', '').$link,
                ],
            ],
            'receivers'     => $receivers,
        ]);
    }


    /**
     * 发送消息模版
     *
     * @param  array  $data  消息数据
     * @return array|mixed|string
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    private function sendMessageByTemplate(array $data = [])
    {
        // 验证参数
        $params = [];

        if (isset($data['system_id'])) {
            $params['systemId'] = (int)$data['system_id'];
        }        // 子系统id(必填)
        if (isset($data['biz_id'])) {
            $params['bizId'] = (int)$data['biz_id'];
        }                 // 因素ID
        if (isset($data['msg_source'])) {
            $params['msgSource'] = $data['msg_source'];
        }          // 消息来源(必填)
        if (isset($data['template_code'])) {
            $params['templateCode'] = $data['template_code'];
        } // 消息模版(必填)
        if (isset($data['channels'])) {
            $params['channels'] = $data['channels'];
        }               // 通道
        if (isset($data['variables'])) {
            $params['variables'] = $data['variables'];
        }            // 变量信息
        if (isset($data['receivers'])) {
            $params['receivers'] = $data['receivers'];
        }            // 接收人

        if ( ! empty($data['site_letter'])) {
            $params['siteLetterComponentStyle'] = $data['site_letter'];
        } // 消息位置
        if ( ! empty($data['pc_url'])) {
            $params['pcURl'] = $data['pc_url'];
        }                              // 消息PC跳转链接

        // 赋值中台token
        if ( ! empty($data['token'])) {
            $params['token'] = $data['token'];
        }

        $url = env('PLATFORM.HOST', '').self::USER.'/MsgSubscribeInstance/SendMessageByTemplate';

        try {
            $response = $this->client->post($url, [
                'json' => $params
            ]);

//            dd($url,$params,$response->getBody()->getContents());
            return json_decode($response->getBody()->getContents(), true);
        } catch (\Exception $e) {
            Log::error('中台消息发送失败', [
                'url'    => $url,
                'params' => $params,
                'error'  => $e->getMessage()
            ]);
            throw $e;
        }
    }
} 