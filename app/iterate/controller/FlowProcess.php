<?php
/**
 *
 * User: 袁志凡
 * Date-Time: 2024/7/5 上午10:33
 */

namespace app\iterate\controller;

use app\iterate\logic\FlowProcessLogic;
use basic\BaseController;
use resp\Result;
use think\App;
use think\Request;
use think\response\Json;

class FlowProcess extends BaseController
{
    private $logic;

    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->logic = new FlowProcessLogic();
    }


    /**
     * 创建
     * @param Request $request
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2024/7/8 下午5:52
     */
    public function create(Request $request)
    {
        $param = $request->post([
            'flow_process_name',
            'flow_process_desc',
            'node_list',
            'project_id',
            'bug_liquidation_time'
        ]);
        $model = $this->logic->create($param);

        return Result::success($model);
    }

    /**
     * 更新
     * @param Request $request
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2024/7/8 下午5:52
     */
    public function update(Request $request)
    {
        $param = $request->post([
            'flow_process_name',
            'flow_process_desc',
            'node_list',
            'flow_process_id',
            'version',
            'bug_liquidation_time'
        ]);
        $model = $this->logic->update($param);

        return Result::success($model);
    }


    /**
     * 删除
     * @param Request $request
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2024/7/8 下午5:52
     */
    public function delete(Request $request)
    {
        $this->logic->delete($request->post('flow_process_id'));

        return Result::success();
    }


    /**
     * 详情
     * @param Request $request
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2024/8/13 下午2:06
     */
    public function detail(Request $request)
    {
        $res = $this->logic->detail($request->get('flow_process_id'));

        return Result::success($res);
    }


    /**
     * 分页查询
     * @param Request $request
     * @return \think\response\Json
     * @throws \think\db\exception\DbException
     * <AUTHOR>
     * @date 2024/7/8 下午5:53
     */
    public function pageQuery(Request $request)
    {
        $param = $request->get([
            'project_id',
        ]);
        $data = $this->logic->pageQuery($param);

        return Result::success($data);
    }

    /**
     * 复制
     * @param $id
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2024/7/8 下午5:54
     */
    public function copy(Request $request)
    {
        $param = $request->post([
            'id',
        ]);

        $res = $this->logic->copy($param['id']);

        return Result::success($res);
    }

    /**
     * 流程下拉数据
     * @param $projectId
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2024/8/13 下午2:07
     */
    public function selector(Request $request)
    {
        $res = $this->logic->selector($request->get('project_id'));

        return Result::success($res);
    }

    /**
     * 流程节点下拉数据
     * @param Request $request
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2024/8/13 下午2:08
     */
    public function nodeSelector(Request $request)
    {
        $res = $this->logic->nodeSelector($request->post('flow_process_id'));

        return Result::success($res);
    }

    /**
     * 更新工作流程图名称
     * @return Json
     * User Long
     * Date 2025/4/14
     */
    public function rename()
    {
        $flowProcessId = $this->request->post('flow_process_id/d');
        $flowProcessName = $this->request->post('flow_process_name/s');

        $this->logic->rename($flowProcessId, $flowProcessName);

        return Result::success();
    }
}
