<?php
/**
 *
 * User: 袁志凡
 * Date-Time: 2024/9/29 14:46
 */

namespace app\work_items\logic;

use app\iterate\model\FlowStatusEnumModel;
use app\project\logic\ProjectClassifyLogic;
use app\project\model\ProjectCategoryModel;
use app\project\model\ProjectModel;
use app\project\model\ProjectUserModel;
use app\work_items\model\ExecutionRecordModel;
use app\work_items\model\PlanUseCaseBugModel;
use app\work_items\model\PlanUseCaseModel;
use app\work_items\model\TestCaseModel;
use app\work_items\model\TestPlanModel;
use app\work_items\model\TestPlanWorkCaseModel;
use app\work_items\model\WorkItemsModel;
use basic\BaseLogic;
use basic\BaseModel;
use exception\NotFoundException;
use exception\ParamsException;
use think\Collection;
use think\db\Query;
use think\Paginator;
use utils\DBTransaction;

class PlanUseCaseLogic extends BaseLogic
{
    /**
     * 添加用例
     * @param $testPlanId    int 计划id
     * @param $newCaseIdList array 测试用例id集合
     * @param $oldCaseIdList array 旧测试用例id集合
     * @return Collection
     * @throws \Throwable
     * <AUTHOR>
     * @date   2024/10/16 15:09
     */
    public function relevancy($testPlanId, $newCaseIdList, $oldCaseIdList)
    {


        try {
            DBTransaction::begin();

//            $removeList = array_diff($oldCaseIdList, $newCaseIdList);
            $newList = array_diff($newCaseIdList, $oldCaseIdList);
//            $newList = array_merge($newList, array_intersect($newCaseIdList, $oldCaseIdList));

            //删除 这里不删除，在关系数据中删除（无任何关系引用此数据）会联动删除此数据
//            $removeList && $this->del($testPlanId, $removeList);

            $result = new Collection();
            if ($newList) {
                $oldList = PlanUseCaseModel::where(['test_plan_id' => $testPlanId, 'test_case_id' => $newList])->select();

                $oldList->where('is_delete', '=', BaseModel::DELETE_YES)->each(function (PlanUseCaseModel $item) {
                    $item->save(['is_delete' => BaseModel::DELETE_NOT]);
                });
                //保存
                $result = (new PlanUseCaseModel)->saveAll(array_map(function ($item) use ($testPlanId) {
                    return [
                        'test_plan_id' => $testPlanId,
                        'test_case_id' => $item,
                    ];
                }, array_diff($newList, $oldList->column('test_case_id'))));
            }


            DBTransaction::commit();

            return $result;
        } catch (\Throwable $e) {
            DBTransaction::rollback();
            throw  $e;
        }

    }


    /**
     * 删除关系
     * @param $planId
     * @param $caseId
     * @return void
     * @throws \Throwable
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date   2024/10/16 15:21
     */
    public function del($planId = null, $caseId = null)
    {

        try {
            DBTransaction::begin();

            $where = [];
            if ( ! is_null($planId)) {
                $where['test_plan_id'] = $planId;
            }
            if ( ! is_null($caseId)) {
                $where['test_case_id'] = $caseId;
            }
            if ( ! $where) {
                throw new ParamsException();
            }


            PlanUseCaseModel::where($where)->select()->each(function (PlanUseCaseModel $model) {
                $model->save(['is_delete' => BaseModel::DELETE_YES]);
            });

            DBTransaction::commit();
        } catch (\Throwable $e) {
            DBTransaction::rollback();
            throw  $e;
        }

    }


    /**
     * 通过目录添加用例
     * @param $testPlanId int 计划id
     * @param $idList     array 用例id集合
     * @param $cntId      int  需求id
     * @return void
     * @throws \Throwable
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date   2024/10/9 14:55
     */
    public function addBugByCategory($testPlanId, $idList, $cntId = 0)
    {

        try {
            DBTransaction::begin();
            WorkItemsModel::$autoSave = false;
            TestPlanModel::$autoSave = false;
            TestCaseModel::$autoSave = false;
            TestPlanLogic::$isTriggerStatistics = false;

            if ($cntId) {
                $oldCaseIdList = TestPlanWorkCaseModel::where(['test_plan_id' => $testPlanId, 'cnt_id' => $cntId])
                    ->where([
                        ['test_case_id', '<>', 0]
                    ])
                    ->column('test_case_id');

                $model = WorkItemsModel::with([
                    'testCaseWork'
                ])->find($cntId);
                if ( ! $model) {
                    throw new NotFoundException("需求不存在");
                }
                //外部需求与用例关系的建立
                $exteriorOldCaseIdList = $model->testCaseWork->column('test_case_id');
                if (array_diff($idList, $exteriorOldCaseIdList)) {
                    (new TestCaseWorkLogic())->relevancy($cntId, array_merge($exteriorOldCaseIdList, $idList), TestCaseWorkLogic::TYPE_CNT_ID);
                }
            } else {
                $oldCaseIdList = TestPlanWorkCaseModel::where(['test_plan_id' => $testPlanId, 'cnt_id' => 0])->column('test_case_id');
            }

            if ($idList) {
                //检查用例是否都存在
                $count = TestCaseModel::findListById($idList)->count();
                if ($count != count($idList)) {
                    throw new ParamsException("存在无效的用例，请刷新页面重试！");
                }
            }


            //增加计划与用例的关系
            $this->relevancy($testPlanId, $idList, $oldCaseIdList);

            //增加计划与需求的关系
            (new TestPlanWorkCaseLogic())->relevancy($testPlanId, $cntId ? array_merge($idList, [0]) : $idList, $cntId);

            WorkItemsModel::$autoSave = true;
            TestPlanModel::$autoSave = true;
            TestCaseModel::$autoSave = true;
            TestPlanLogic::$isTriggerStatistics = true;
            WorkItemsModel::actualSaving();
            TestPlanModel::actualSaving();
            TestCaseModel::actualSaving();
            TestPlanLogic::triggerStatistics($testPlanId);

            DBTransaction::commit();
        } catch (\Throwable $e) {
            DBTransaction::rollback();
            throw  $e;
        }
    }


    /**
     * 通过需求添加用例，更新需求的用例
     * @param $testPlanId
     * @param $cntIdList
     * @return void
     * @throws \Throwable
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date   2024/10/15 14:51
     */
    public function addBugByDemand($testPlanId, $cntIdList)
    {

        try {
            DBTransaction::begin();
            WorkItemsModel::$autoSave = false;
            TestPlanModel::$autoSave = false;
            TestCaseModel::$autoSave = false;
            TestPlanLogic::$isTriggerStatistics = false;

            if (is_array($cntIdList)) {
                (new TestPlanWorkCaseLogic)->relevancy($testPlanId, 0, $cntIdList);

            } else {
                $allData = TestPlanWorkCaseModel::where([
                    'test_plan_id' => $testPlanId,
                    'test_case_id' => 0,
                ])->select();

                (new TestPlanWorkCaseLogic)->relevancy($testPlanId, 0, array_merge($allData->column('cnt_id'), [$cntIdList]));
            }

            //添加计划与需求下的用例的关系
            $demandList = WorkItemsModel::findDemandByIdListWithTestCase($cntIdList);
            foreach ($demandList as $demand) {
                $this->addBugByCategory($testPlanId, $demand->testCaseWork->column('test_case_id'), $demand->cnt_id);
            }

            WorkItemsModel::$autoSave = true;
            TestPlanModel::$autoSave = true;
            TestCaseModel::$autoSave = true;
            TestPlanLogic::$isTriggerStatistics = true;
            WorkItemsModel::actualSaving();
            TestPlanModel::actualSaving();
            TestCaseModel::actualSaving();
            TestPlanLogic::triggerStatistics($testPlanId);

            DBTransaction::commit();
        } catch (\Throwable $e) {
            DBTransaction::rollback();
            throw  $e;
        }

    }

    /**
     * 删除用例
     * @param $planId  int  计划id
     * @param $caseId  array 用例id集合
     * @param $cntId   int  需求id
     * @param $link    bool  是否联动删除计划外关系
     * @return void
     * <AUTHOR>
     * @date   2024/10/9 15:16
     */
    public function delCase($planId, $caseId, $cntId = 0, $link = false)
    {
        DBTransaction::begin();

        try {

            (new TestPlanWorkCaseLogic())->del($planId, $caseId, $cntId);

            $link && $cntId && (new TestCaseWorkLogic())->del($cntId, $caseId);

            DBTransaction::commit();
        } catch (\Throwable $e) {
            DBTransaction::rollback();
            throw  $e;
        }
    }

    /**
     * 删除需求
     * @param $planId
     * @param $cntId
     * @return void
     * @throws \Throwable
     * <AUTHOR>
     * @date   2024/10/11 11:49
     */
    public function delDemand($planId, $cntId)
    {
        DBTransaction::begin();
        try {

            WorkItemsModel::$autoSave = false;
            TestPlanModel::$autoSave = false;
            TestCaseModel::$autoSave = false;
            TestPlanLogic::$isTriggerStatistics = false;

            (new TestPlanWorkCaseLogic())->del($planId, null, $cntId);

            WorkItemsModel::$autoSave = true;
            TestPlanModel::$autoSave = true;
            TestCaseModel::$autoSave = true;
            TestPlanLogic::$isTriggerStatistics = true;
            WorkItemsModel::actualSaving();
            TestPlanModel::actualSaving();
            TestCaseModel::actualSaving();
            TestPlanLogic::triggerStatistics($planId);

            DBTransaction::commit();
        } catch (\Throwable $e) {
            DBTransaction::rollback();
            throw  $e;
        }
    }

    /**
     * 删除目录
     * @param $planId
     * @param $categoryId
     * @return void
     * @throws \Elastic\Elasticsearch\Exception\ClientResponseException
     * @throws \Elastic\Elasticsearch\Exception\ServerResponseException
     * @throws \Throwable
     * <AUTHOR>
     * @date   2024/10/11 11:50
     */
    public function delCategory($planId, $categoryId)
    {
        DBTransaction::begin();
        try {
            WorkItemsModel::$autoSave = false;
            TestPlanModel::$autoSave = false;
            TestCaseModel::$autoSave = false;
            TestPlanLogic::$isTriggerStatistics = false;


            $plan = TestPlanModel::findById($planId);
            if ( ! $plan) {
                throw new NotFoundException();
            }
            $projectId = $plan->toDetail()['project_id'];

            if ( ! is_array($categoryId)) {
                $categoryId = [$categoryId];
            }
            $categoryChildList = [];
//            if ($categoryId != '-1') {//未分类无需查询后代分类
//            }
            //获取指定类型，项目的所有分类，供构建树结构
            $categoryList = ProjectClassifyLogic::getListByType($projectId, BaseModel::SETTING_TYPE_TEST_CASE)->toArray();

            //所有后代目录的id
            $categoryChildList = [];
            foreach ($categoryId as $item) {
                $categoryChildList = array_merge((new WorkItemsLogic)->getDescendants($categoryList, $item, 'pid', 'project_category_id'), $categoryChildList);
            }



            //通过目录id集合查询所有用例
            $caseIdList = TestCaseLogic::getInstance()->esSearch([
                ['field_name' => 'category_id', 'value' => array_merge($categoryId, $categoryChildList), 'type' => 'selector'],
            ], TestCaseLogic::NOT_PAGE_MAX)->getCollection()->column('test_case_id');

            (new TestPlanWorkCaseLogic())->del($planId, $caseIdList, 0);

            WorkItemsModel::$autoSave = true;
            TestPlanModel::$autoSave = true;
            TestCaseModel::$autoSave = true;
            TestPlanLogic::$isTriggerStatistics = true;
            WorkItemsModel::actualSaving();
            TestPlanModel::actualSaving();
            TestCaseModel::actualSaving();
            TestPlanLogic::triggerStatistics($planId);
            DBTransaction::commit();
        } catch (\Throwable $e) {
            DBTransaction::rollback();
            throw  $e;
        }
    }


    /**
     * 分页查询
     * 大致思路
     * 获取结果用例（不分页）
     * 获取需求
     * 目录，如果当前分页是最后一页需展示目录以及目录下的用例
     * 给以上三种参数设置好node_id,node_pid,然后构建树结构数据
     * 最后递归书统计需求/目录下的用例数、已执行用例数
     * @param $planId
     * @param $fieldList
     * @param $searchParams
     * @param array $order 排序参数
     * @return Paginator|\think\paginator\driver\Bootstrap
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date   2024/10/12 14:59
     */
    public function pageQuery($planId, $fieldList, $searchParams, $order = [])
    {
        $plan = TestPlanModel::findById($planId);
        if ( ! $plan) {
            throw new NotFoundException();
        }
        $plan = $plan->toDetail();

        //结果用例集合
        $caseList = TestCaseLogic::getInstance()->pageQuery([
            'project_id'   => $plan['project_id'],
            'field_list'   => array_merge($fieldList, [
                'category_id',
                'plan_list',
                'case_tep',
                'preconditions',
                'expected_results',
            ]),
            'searchParams' => $searchParams,
            'test_plan_id' => $plan['test_plan_id'],
            'order'        => $order,
        ], TestCaseLogic::NOT_PAGE_MAX)->each(function ($item) use ($planId) {
            $item['node_type'] = 3;
            $item['id'] = $item['test_case_id'];
            $planData = $item['plan_list'][$planId] ?? [];
            unset($item['plan_list']);

            return array_merge($item, $planData);
        })->getCollection();

        $planUseCaseResult = TestPlanWorkCaseLogic::getListByPlanIdAndCaseId(
            $plan['test_plan_id'], array_merge(
                $caseList->column('test_case_id'),//通过结果用例结合查询对应的关系
                $searchParams ? [] : [0]//如果搜索参数为空，那么会展示空需求
            )
        );
        $cntIdList = $planUseCaseResult->where('test_case_id', '=', 0)->column('cnt_id');
        $planUseCaseResult = $planUseCaseResult->where('test_case_id', '!=', 0);


        $caseListWithKey = $caseList->column(null, 'test_case_id');
        $formatCaseList = [];//用例与目录，用例与需求的关系集合
        $planUseCaseResult->each(function ($item) use ($caseListWithKey, &$formatCaseList) {
            $data = [
                'cnt_id'                 => $item['cnt_id'],
                'test_plan_work_case_id' => $item['test_plan_work_case_id'],
            ];
            $data['node_id'] = 'case-'.$item['cnt_id'].'-'.$item['test_case_id'];
            if ($item['cnt_id'] != 0) {
                $data['node_pid'] = 'cnt-'.$item['cnt_id'];
            } else {
                $data['node_pid'] = 'category-'.$caseListWithKey[$item['test_case_id']]['category_id'];
            }

            $formatCaseList[] = array_merge($data, $caseListWithKey[$item['test_case_id']]);

            return $item;
        });


        $demandSearchParams = [
            ['field_name' => 'cnt_id', 'value' => $cntIdList, 'type' => 'selector'],
            ['field_name' => 'cnt_type', 'value' => WorkItemsModel::CNT_TYPE_DEMAND, 'type' => 'term', 'operate_type' => 'equal']
        ];

        //需求集合
        $demandResult = (new WorkItemsLogic)->pageQuery([
            'project_id'   => $plan['project_id'],
            'field_list'   => ['cnt_id', 'title'],
            'searchParams' => $demandSearchParams,
        ])->each(function ($item) {
            $item['node_type'] = 1;
            $item['id'] = $item['cnt_id'];
            $item['node_id'] = 'cnt-'.$item['cnt_id'];
            $item['node_pid'] = 0;

            return $item;
        });

        $categoryList = new Collection();
        if ($demandResult->currentPage() >= $demandResult->lastPage()) {
            //目录集合
            $project = ProjectModel::findById((int)$plan['project_id']);
            if ( ! $project) {
                throw new NotFoundException();
            }
            $categoryList = ProjectClassifyLogic::list((int)$plan['project_id'], ProjectCategoryModel::PROJECT_CATEGORY_TYPE_TEST_CASE)->unshift([
                'project_category_id' => '-1',
                'category_name'       => "未分类",
                'pid'                 => 0,
            ])->each(function ($item) use ($project) {
                $item['node_type'] = 2;
                $item['id'] = $item['project_category_id'];
                $item['title'] = $item['category_name'];
                $item['node_id'] = 'category-'.$item['project_category_id'];
                $item['node_pid'] = $item['pid'] == 0 ? 0 : 'category-'.$item['pid'];
                $item['project_id'] = $project['project_id'];
                $item['project_name'] = $project['project_name'];

                return $item;
            });
        }
//        dd($formatCaseList, $categoryList->toArray(), $demandResult->getCollection()->toArray());
        $tree = $this->buildTree(array_merge($demandResult->getCollection()->toArray(), $categoryList->toArray(), $formatCaseList));
        foreach ($tree as &$item) {
            //统计用例数，已执行用例数
            $this->countLeafNodes($item);
        }
        //清楚无用例的目录
        if ( ! $categoryList->isEmpty()) {
            $tree = array_filter($tree, function ($item) {
                if ($item['node_type'] == 2 && $item['count'] == 0) {
                    return false;
                }

                return true;
            });
        }


        return $demandResult->setCollection(new Collection(array_values($tree)));

    }


    /**
     * 格式化为树
     * @param $data
     * @param $parentId
     * @return array
     * <AUTHOR>
     * @date   2024/8/28 11:59
     */
    public function buildTree($data)
    {
        $tree = [];
        $itemsById = [];

        // 先将数据按 id 重新索引
        foreach ($data as $item) {
            $itemsById[$item['node_id']] = $item;
            $itemsById[$item['node_id']]['children'] = []; // 初始化子节点
        }

        // 遍历所有项，构建树
        foreach ($itemsById as &$item) {
            // 如果 parent_id 为 null 或者不存在于数据中，则是顶级节点

            if ($item['node_pid'] === 0) {
                $tree[] = &$item;
            } elseif ( ! isset($itemsById[$item['node_pid']])) {
                continue;
            } else {
                // 如果有父节点，将当前节点作为父节点的子节点
                $itemsById[$item['node_pid']]['children'][] = &$item;
            }
        }

        return $tree;
    }


    /**
     * 递归计算叶子结点数(用例数，已执行用例数)
     * @param $node
     * @return void
     * <AUTHOR>
     * @date   2024/10/29 14:44
     */
    function countLeafNodes(&$node)
    {
        // 如果当前节点是叶子节点（数据节点），返回 1
        if ($node['node_type'] == 3) {
            $node['count'] = 1;
            $node['exeCount'] = $node['plan_execution_times'] > 0 ? 1 : 0;

            return;
        }

        // 遍历每个子节点，递归计算其下的叶子节点数量
        foreach ($node['children'] as $key => &$child) {
            $this->countLeafNodes($child);
            if ( ! $child['count']) {//清楚无数据的子目录
                unset($node['children'][$key]);
            }
        }
        $node['children'] = array_values($node['children']);
        // 在当前节点上记录其子树中叶子节点的总数
        $node['count'] = array_sum(array_column($node['children'], 'count'));
        $node['exeCount'] = array_sum(array_column($node['children'], 'exeCount'));
    }


    /**
     * 联动保存用例PlanList
     * @param  PlanUseCaseModel  $model
     * @return void
     * <AUTHOR>
     * @date   2024/10/10 17:40
     */
    public function triggerSaveCasePlanList(PlanUseCaseModel $model)
    {
        $latest = ExecutionRecordModel::getLatestByPlanUseCaseId($model->plan_use_case_id);

        if ($model->is_delete == BaseModel::DELETE_YES) {
            TestCaseLogic::savePlanList($model->test_case_id, $model->test_plan_id, []);
        } else {
            $casePlanData = [
                'test_plan_id'         => $model->test_plan_id,
                'plan_execution_times' => $model->execution_times,
                'plan_bug_count'       => $model->bug_count,
                'plan_create_by'       => 0,
                'plan_create_at'       => null,
                'plan_result'          => 0,
            ];
//        dd($this->refresh()->toArray());
            if ($latest) {
                $casePlanData['plan_create_by'] = $latest->create_by;
                $casePlanData['plan_create_at'] = $latest->create_at;
                $casePlanData['plan_result'] = $latest->result;
            }


            TestCaseLogic::savePlanList($model->test_case_id, $model->test_plan_id, $casePlanData);
        }

    }


    /**
     * 根据计划id，用例id查询集合
     * @param $planId
     * @param $caseIdList
     * @return PlanUseCaseModel[]|array|Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date   2024/10/12 15:27
     */
    public static function getListByPlanIdAndCaseId($planId, $caseIdList)
    {
        $where = ['test_plan_id' => $planId, 'test_case_id' => $caseIdList];

        return PlanUseCaseModel::where($where)
            ->select();
    }


    /**
     * 执行
     * @param $testPlanWorkCaseId int 关系id
     * @param $result
     * @param $remark
     * @param $cntIdList
     * @return void
     * @throws \Throwable
     * <AUTHOR>
     * @date   2024/10/31 14:24
     */
    public function execute($testPlanWorkCaseId, $result, $remark = '', $cntIdList = [])
    {
        $testPlanWorkCaseModel = TestPlanWorkCaseModel::findById($testPlanWorkCaseId);
        if ( ! $testPlanWorkCaseModel) {
            throw new NotFoundException();
        }
        $model = PlanUseCaseModel::getByPlanIdAndCaseId($testPlanWorkCaseModel->test_plan_id, $testPlanWorkCaseModel->test_case_id);
        if ( ! $model) {
            throw new NotFoundException();
        }
        try {
            DBTransaction::begin();

            $recordModel = ExecutionRecordModel::create([
                'test_plan_id'     => $testPlanWorkCaseModel->test_plan_id,
                'plan_use_case_id' => $model->plan_use_case_id,
                'result'           => $result,
                'remark'           => $remark,
            ]);


            $cntIdList = array_unique($cntIdList);
            $oldCntIdList = $this->recordBugList($testPlanWorkCaseModel->test_plan_id, $testPlanWorkCaseModel->test_case_id);
            if (array_intersect($oldCntIdList, $cntIdList)) {
                throw new ParamsException("此缺陷已存在执行明细中");
            }
            if ($cntIdList) {
                (new PlanUseCaseBugModel)->saveAll(array_map(function ($item) use ($recordModel) {
                    return [
                        'plan_use_case_id'    => $recordModel->plan_use_case_id,
                        'test_plan_id'        => $recordModel->test_plan_id,
                        'execution_record_id' => $recordModel->execution_record_id,
                        'cnt_id'              => $item,
                    ];
                }, $cntIdList));


                if ($testPlanWorkCaseModel->cnt_id) {
                    WorkItemsModel::findListById($cntIdList)->each(function (WorkItemsModel $item) use ($testPlanWorkCaseModel) {
                        $detail = $item->toDetail();
                        if ($detail['parent_id'] == 0) {
                            $item->save(['parent_id' => $testPlanWorkCaseModel->cnt_id]);
                        }
                    });
                }
            }


            $model->bug_count += count($cntIdList);
            $model->execution_times += 1;
            $model->save();


            DBTransaction::commit();
        } catch (\Throwable $e) {
            DBTransaction::rollback();
            throw  $e;
        }


    }


    /**
     * 批量执行
     * @param $testPlanWorkCaseIdList array 关系id集合
     * @param $result
     * @param $remark
     * @return void
     * @throws \Throwable
     * <AUTHOR>
     * @date   2024/10/31 14:25
     */
    public function batchExecute($testPlanWorkCaseIdList, $result, $remark = '')
    {
        try {
            DBTransaction::begin();

            //去重，同个用例只执行一次
            $kvList = TestPlanWorkCaseModel::findListById($testPlanWorkCaseIdList)->column('test_plan_work_case_id', 'test_case_id');

            foreach ($kvList as $v) {
                $this->execute($v, $result, $remark);
            }

            DBTransaction::commit();
        } catch (\Throwable $e) {
            DBTransaction::rollback();
            throw  $e;
        }

    }


    /**
     * 执行用例明细
     * @param $testPlanId
     * @param $testCaseId
     * @return mixed
     * <AUTHOR>
     * @date   2024/10/12 17:03
     */
    public function recordList($testPlanId, $testCaseId)
    {
        $model = PlanUseCaseModel::getByPlanIdAndCaseId($testPlanId, $testCaseId);
        if ( ! $model) {
            throw new NotFoundException();
        }

        return $model->record()->field(ExecutionRecordModel::LIST_FIELDS)->with([
            'bugs' => function (Query $query) {
                $query->where(['is_delete' => BaseModel::DELETE_NOT])
                    ->field(PlanUseCaseBugModel::LIST_FIELDS)
                    ->with(['bug']);
            }
        ])
            ->order(['execution_record_id desc'])
            ->select()
            ->each(function ($item) {
                foreach ($item['bugs'] as &$bug) {
                    $bug['title'] = $bug['bug']->toDetail()['title'];
                    unset($bug['bug']);
                }
                $item['result_label'] = match ($item['result']) {
                    ExecutionRecordModel::PASS => '通过',
                    ExecutionRecordModel::CLOG => '阻塞',
                    ExecutionRecordModel::NOT_PASSED => '不通过'
                };

                return $item;
            });
    }

    /**
     * 用例执行明细中的缺陷删除
     * @param $id                 int 用例与缺陷的关系id PlanUseCaseBug
     * @param $testPlanWorkCaseId int 关系id，用于删除时解除与需求的关系 TestPlanWorkCase
     * @return void
     * @throws \Throwable
     * <AUTHOR>
     * @date   2024/10/31 14:37
     */
    public function delRecordBug($id, $testPlanWorkCaseId)
    {
        try {
            DBTransaction::begin();

            $model = PlanUseCaseBugModel::findById($id);
            if ( ! $model) {
                throw new NotFoundException();
            }

            $model->save(['is_delete' => BaseModel::DELETE_YES]);

            $model->planUseCase->bug_count -= 1;
            $model->planUseCase->save();
            $testPlanWorkCaseModel = TestPlanWorkCaseModel::findById($testPlanWorkCaseId);
            if ( ! $testPlanWorkCaseModel) {
                throw new NotFoundException();
            }
            if ($testPlanWorkCaseModel->cnt_id) {
                WorkItemsModel::findListById($model->cnt_id)->each(function (WorkItemsModel $item) use ($testPlanWorkCaseModel) {
                    $detail = $item->toDetail();
                    if ($detail['parent_id'] == $testPlanWorkCaseModel->cnt_id) {
                        $item->save(['parent_id' => 0]);
                    }
                });
            }


            DBTransaction::commit();
        } catch (\Throwable $e) {
            DBTransaction::rollback();
            throw  $e;
        }

    }

    /**
     * 通过缺陷id删除计划中的数据
     * @param $cntId
     * @return void
     * @throws \Throwable
     * <AUTHOR>
     * @date   2024/10/31 13:03
     */
    public function delRecordBugByCntId($cntId)
    {
        try {
            DBTransaction::begin();

            PlanUseCaseBugModel::getListByCntId($cntId)->each(function (PlanUseCaseBugModel $model) {
                $model->save(['is_delete' => BaseModel::DELETE_YES]);

                $model->planUseCase->bug_count -= 1;
                $model->planUseCase->save();
            });

            DBTransaction::commit();
        } catch (\Throwable $e) {
            DBTransaction::rollback();
            throw  $e;
        }

    }

    /**
     * 获取用例执行明细所有的关联缺陷
     * @param $testPlanId
     * @param $testCaseId
     * @return mixed
     * <AUTHOR>
     * @date   2024/10/12 17:03
     */
    public function recordBugList($testPlanId, $testCaseId)
    {
        $model = PlanUseCaseModel::getByPlanIdAndCaseId($testPlanId, $testCaseId);
        if ( ! $model) {
            throw new NotFoundException();
        }

        $result = [];
        $model->record()->with(['bugs'])->select()->each(function ($item) use (&$result) {
            $result = array_merge($result, array_column($item->bugs->toArray(), 'cnt_id'));
        });

        return $result;
    }


    /**
     * 将关联的缺陷追加到最新的一条执行明细中，必须已有执行明细才关联
     * @param $testPlanWorkCaseId int 关系id
     * @param $cntIdList
     * @return void
     * @throws \Throwable
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date   2024/10/31 14:45
     */
    public function addBugToLatestRecord($testPlanWorkCaseId, $cntIdList)
    {
        $testPlanWorkCaseModel = TestPlanWorkCaseModel::findById($testPlanWorkCaseId);
        if ( ! $testPlanWorkCaseModel) {
            throw new NotFoundException();
        }
        $model = PlanUseCaseModel::getByPlanIdAndCaseId($testPlanWorkCaseModel->test_plan_id, $testPlanWorkCaseModel->test_case_id);
        if ( ! $model) {
            throw new NotFoundException();
        }
        try {
            DBTransaction::begin();

            $bugs = [];
            $recordList = $model->record()->order(['execution_record_id desc'])->with(['bugs'])->select()->each(function ($item) use (&$bugs) {
                $bugs += array_column($item->bugs->toArray(), 'cnt_id');
            });

            $first = $recordList->first();
            if ( ! $first) {
                throw new ParamsException("此用例尚未执行,无法关联缺陷");
            }

            if (array_intersect($cntIdList, $bugs)) {
                throw new ParamsException("此缺陷已存在执行明细中");
            }

            $cntIdList = array_unique($cntIdList);
            if ($cntIdList) {
                (new PlanUseCaseBugModel)->saveAll(
                    (new Collection($cntIdList))->each(function ($item) use ($model, $first) {
                        return [
                            'plan_use_case_id'    => $model->plan_use_case_id,
                            'cnt_id'              => $item,
                            'test_plan_id'        => $model->test_plan_id,
                            'execution_record_id' => $first->execution_record_id,
                        ];
                    })->toArray()
                );

                if ($testPlanWorkCaseModel->cnt_id) {
                    WorkItemsModel::findListById($cntIdList)->each(function (WorkItemsModel $item) use ($testPlanWorkCaseModel) {
                        $detail = $item->toDetail();
                        if ($detail['parent_id'] == 0) {
                            $item->save(['parent_id' => $testPlanWorkCaseModel->cnt_id]);
                        }
                    });
                }
            }


            $model->bug_count += count($cntIdList);
            $model->save();

//            $this->triggerSaveCasePlanList($model);


            DBTransaction::commit();
        } catch (\Throwable $e) {
            DBTransaction::rollback();
            throw  $e;
        }

    }

    /**
     * 统计
     * @return array
     * <AUTHOR>
     * @date   2024/10/14 14:55
     */
    public function statistics($planId)
    {
        $model = TestPlanModel::with([
            'planUseCases' => function (Query $query) {
                $query->with([
                    'record' => function (Query $query) {
                        $query->with([
                            'bugs' => function (Query $query) {
                                $query->where(['is_delete' => BaseModel::DELETE_NOT]);
                            }
                        ])->where(['is_delete' => BaseModel::DELETE_NOT]);
                    }
                ])->where(['is_delete' => BaseModel::DELETE_NOT]);
            }, 'testPlanWorkCase'
        ])->find($planId);

        if ( ! $model) {
            throw new NotFoundException();
        }

        $bugCount = 0;
        $executionData = [
            'pass'         => 0,//通过数
            'not_passed'   => 0,//不通过数
            'clog'         => 0,//阻塞数
            'not_executed' => 0, //未执行数
        ];
        $model->planUseCases->each(function (PlanUseCaseModel $item) use (&$bugCount, &$executionData) {
            $item->record->each(function (ExecutionRecordModel $item) use (&$bugCount) {
                $bugCount += $item->bugs->count();
            });
            match ($item->record->last()['result'] ?? ExecutionRecordModel::NOT_EXECUTED) {
                ExecutionRecordModel::NOT_EXECUTED => $executionData['not_executed'] += 1,
                ExecutionRecordModel::PASS => $executionData['pass'] += 1,
                ExecutionRecordModel::CLOG => $executionData['clog'] += 1,
                ExecutionRecordModel::NOT_PASSED => $executionData['not_passed'] += 1,
                default => throw new ParamsException()
            };
        });

        $caseCount = count(array_unique($model->planUseCases->column('test_case_id')));
        $demandCount = $model->testPlanWorkCase->where('test_case_id', '=', 0)->count();
        //已关联用例的需求数
        $hasCaseDemandCount = count($model->testPlanWorkCase->where('test_case_id', '<>', 0)->where('cnt_id', '<>', 0)->column('test_case_id', 'cnt_id'));

        return [
            'demand_count'        => $demandCount,//需求数
            'case_count'          => $caseCount,//用例数
            'bug_count'           => $bugCount,//缺陷数
            'execution_data'      => $executionData,//执行数据
            'implementation_rate' => (int)(($caseCount == 0 ? 0 : bcdiv((string)($executionData['pass'] + $executionData['not_passed']), (string)$caseCount, 2)) * 100),//测试执行率
            'pass_rate'           => (int)((($executionData['pass'] + $executionData['not_passed']) == 0 ? 0 : bcdiv((string)$executionData['pass'], (string)($executionData['pass'] + $executionData['not_passed']), 2)) * 100),//测试通过率
            'use_case_coverage'   => (int)(($demandCount == 0 ? 0 : bcdiv((string)($hasCaseDemandCount), (string)$demandCount, 2)) * 100),//用例覆盖率
        ];

    }

    /**
     * 缺陷的测试用例tab页
     * @param $cntId int 缺陷id
     * @return Paginator
     * @throws \think\db\exception\DbException
     * <AUTHOR>
     * @date   2024/10/16 17:52
     */
    public function flawCaseList($cntId)
    {
//        $cntId = 1753;
        $result = [];
        $alias = (new ExecutionRecordModel)->getTable();
        $recordList = ExecutionRecordModel::status()
            ->alias($alias)
            ->hasWhere('bugs', function (Query $query) use ($cntId) {
                $query->where(['cnt_id' => $cntId]);
            })
            ->with([
                'useCase' => function (Query $query) {
                    $query->with(['case', 'plan']);
                }
            ])
            ->where([
                $alias.'.is_delete' => BaseModel::DELETE_NOT
            ])
            ->order('create_at desc')
            ->paginate(getPageSize())->each(function ($item) use (&$result) {

                $case = $item['useCase']['case']->toDetail();
                $plan = $item['useCase']['plan']->toDetail();
                $result[] = [
                    'case_title'     => $case['title'],
                    'test_case_id'   => $case['test_case_id'],
                    'plan_title'     => $plan['title'],
                    'test_plan_id'   => $plan['test_plan_id'],
                    'create_by'      => $item['create_by'],
                    'create_by_name' => $item['create_by_name'],
                    'create_at'      => $item['create_at'],
                    'result'         => $item['result'],
                    'result_label'   => match ($item['result']) {
                        ExecutionRecordModel::PASS => '通过',
                        ExecutionRecordModel::CLOG => '阻塞',
                        ExecutionRecordModel::NOT_PASSED => '不通过'
                    },
                ];
            });

//        return $recordList->setCollection($result)->getCollection()->toArray();
        return $recordList->setCollection(new Collection($result));

    }

    /**
     * 测试用例的的缺陷tab页
     * @param $caseId int 测试用例id
     * @return Paginator
     * @throws \think\db\exception\DbException
     * <AUTHOR>
     * @date   2024/10/16 17:52
     */
    public function caseFlawList($caseId)
    {
//        $caseId = 62;
        $result = [];
        $alias = (new PlanUseCaseBugModel)->getTable();
        $recordList = PlanUseCaseBugModel::status()
            ->alias($alias)
            ->hasWhere('planUseCase', function (Query $query) use ($caseId) {
                $query->where(['test_case_id' => $caseId])
                    ->with(['plan']);
            })
            ->with(['recordBug', 'bug'])
            ->where([
                $alias.'.is_delete' => BaseModel::DELETE_NOT,
//                $alias . '.cnt_id' => 1753,
            ])
            ->order('create_at desc')
            ->paginate(getPageSize())->each(function ($item) use (&$result) {

                $plan = $item['planUseCase']['plan']->toDetail();
                $cnt = $item['bug']->toDetail();
                $result[] = [
                    'cnt_id'         => $cnt['cnt_id'],
                    'cnt_title'      => $cnt['title'],
                    'test_plan_id'   => $plan['test_plan_id'],
                    'plan_title'     => $plan['title'],
                    'severity_level' => $cnt['severity_level'] ?? null,

                    'handler_uid'    => $cnt['handler_uid'] ?? null,
                    'status_enum_id' => $cnt['status_enum_id'] ?? null,

                    'create_by'      => $item['recordBug']['create_by'],
                    'create_by_name' => $item['recordBug']['create_by_name'],
                    'create_at'      => $item['recordBug']['create_at'],
                ];
            });

        $statusList = FlowStatusEnumModel::findListById(array_unique(array_column($result, 'status_enum_id')))->column(null, 'status_enum_id');
        $userList = ProjectUserModel::selectListById($this->flattenArray(array_column($result, 'handler_uid')))->column('user_name', 'user_id');
        $result = new Collection($result);
        $result->each(function ($item) use ($statusList, $userList) {
            $item['status_enum_name'] = $item['status_enum_id'] ? ($statusList[$item['status_enum_id']]['name']) ?? '' : '';
            $item['status_colour'] = $item['status_enum_id'] ? ($statusList[$item['status_enum_id']]['colour']) ?? '' : '';
            $item['handler_name'] = $this->getByIndex($userList, $item['handler_uid']);

            return $item;
        });


//        return $recordList->setCollection(new Collection($result))->getCollection()->toArray();
        return $recordList->setCollection($result);

    }

    private function flattenArray(array $array): array
    {
        $result = [];
        array_walk_recursive($array, function ($item) use (&$result) {
            $result[] = $item;
        });
        return array_unique(array_filter($result));
    }

    private function getByIndex($arr, $index)
    {
        $result = [];
        if (is_array($index)) {
            foreach ($index as $v) {
                if (isset($arr[$v])) {
                    $result[] = $arr[$v];
                }
            }
        } else {
            if (isset($arr[$index])) {
                $result[] = $arr[$index];

            }
        }
        return implode(',', $result);
    }


    /**
     * 测试计划的缺陷tab页
     * @param $planId int 测试用例id
     * @return Paginator
     * @throws \think\db\exception\DbException
     * <AUTHOR>
     * @date   2024/10/16 17:52
     */
    public function planFlawList($planId)
    {
//        $planId = 6;
        $result = [];
        $alias = (new PlanUseCaseBugModel)->getTable();
        $cntIdList = PlanUseCaseBugModel::status()
            ->alias($alias)
            ->hasWhere('planUseCase', function (Query $query) use ($planId) {
                $query->where(['test_plan_id' => $planId]);
            })
            ->with(['bug'])
            ->where([
                $alias.'.is_delete' => BaseModel::DELETE_NOT,
//                $alias . '.cnt_id' => 1753,
            ])
            ->order('create_at desc')
            ->select()->column('cnt_id');

        $cntList = WorkItemsModel::status()->where(['cnt_id' => $cntIdList])
            ->paginate(getPageSize())->each(function ($item) use (&$result) {
                $cnt = $item->toDetail();
                $result[] = [
                    'cnt_id'         => $cnt['cnt_id'],
                    'cnt_title'      => $cnt['title'],
                    'parent_id'      => $cnt['parent_id'],
                    'status_enum_id' => $cnt['status_enum_id'] ?? null,
                    'severity_level' => $cnt['severity_level'] ?? null,
                    'priority'       => $cnt['priority'] ?? null,
                    'handler_uid'    => $cnt['handler_uid'] ?? null,

                    'create_by'      => $cnt['create_by'],
                    'create_by_name' => $cnt['create_by_name'],
                    'create_at'      => $cnt['create_at'],
                ];
            });
        $parentList = WorkItemsModel::findListById(array_unique(array_column($result, 'parent_id')))->each(function ($item) {
            $item['title'] = $item->toDetail()['title'];
        })->column('title', 'cnt_id');
        $statusList = FlowStatusEnumModel::findListById(array_unique(array_column($result, 'status_enum_id')))->column(null, 'status_enum_id');


        //handler_uid是数组
        $userList = ProjectUserModel::selectListById($this->flattenArray(array_column($result, 'handler_uid')))->column('user_name', 'user_id');

        $result = new Collection($result);
        $result->each(function ($item) use ($parentList, $statusList, $userList) {
            $item['parent_title'] = $item['parent_id'] ? ($parentList[$item['parent_id']]) ?? '' : '';
            $item['status_enum_name'] = $item['status_enum_id'] ? ($statusList[$item['status_enum_id']]['name']) ?? '' : '';
            $item['status_colour'] = $item['status_enum_id'] ? ($statusList[$item['status_enum_id']]['colour']) ?? '' : '';
            $item['handler_name'] = $this->getByIndex($userList, $item['handler_uid']);


            return $item;
        });


//        return $recordList->setCollection($result)->getCollection()->toArray();

        return $cntList->setCollection($result);

    }

    /**
     * 执行记录结果下拉
     * @return array[]
     * <AUTHOR>
     * @date   2024/10/17 15:45
     */
    public function recordResultSelector()
    {
        return [
            ['value' => ExecutionRecordModel::PASS, 'label' => '通过'],
            ['value' => ExecutionRecordModel::CLOG, 'label' => '阻塞'],
            ['value' => ExecutionRecordModel::NOT_PASSED, 'label' => '不通过'],
        ];
    }

    /**
     * 执行记录结果下拉
     * @return array[]
     * <AUTHOR>
     * @date   2024/10/17 15:45
     */
    public function recordResultAddNotExecutedSelector()
    {
        return [
            ['value' => ExecutionRecordModel::PASS, 'label' => '通过'],
            ['value' => ExecutionRecordModel::CLOG, 'label' => '阻塞'],
            ['value' => ExecutionRecordModel::NOT_PASSED, 'label' => '不通过'],
            ['value' => ExecutionRecordModel::NOT_EXECUTED, 'label' => '未执行'],
        ];
    }

    /**
     * 获取用例id集合
     * @param $planId
     * @param $cntId
     * @return TestPlanWorkCaseModel[]|array|Collection
     * <AUTHOR>
     * @date   2024/10/18 14:05
     */
    public function getCaseIdListByPlanId($planId, $cntId = null)
    {
        $where = [
            ['test_plan_id', '=', $planId]
        ];

        if (is_null($cntId)) {
            $where[] = ['cnt_id', '=', 0];
        } else {
            $where[] = ['cnt_id', '=', $cntId];
            $where[] = ['test_case_id', '<>', 0];
        }

        return TestPlanWorkCaseModel::where($where)->column('test_case_id');

    }
}
