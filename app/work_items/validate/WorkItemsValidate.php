<?php
/**
 * Desc 示例 - 验证器
 * User
 * Date 2024/07/03
 */

namespace app\work_items\validate;

use app\microservice\model\MicroserviceModel;
use basic\BaseValidate;

class WorkItemsValidate extends BaseValidate
{
    protected $rule = [
        'cnt_id|工作项Id' => 'require|number|gt:0',
        'project_id|项目ID' => 'require|number|gt:0',
        'cnt_type|类型' => 'require|in:1,2,3',
        'title|标题' => 'require|checkEmoji',
        'type_id|类别' => 'require|number|gt:0',
        'status_enum_id|状态' => 'number|gt:0',
        'iteration_id|迭代' => 'number|gt:0',
        'parent_id|父级ID' => 'number|gt:0',
        'priority_enum_id|优先级' => 'number|gt:0',
        'category_setting_id|子分类ID' => 'number|gt:0',

        'cnt_id_list|关联数据' => 'array',
        'cnt_ids|工作项ID' => 'require|array',
        'target_status|目标状态' => 'require|integer|gt:0',
        'update_empty_only|仅更新空字段' => 'boolean',
        'update_fields|更新字段' => 'require|array',
    ];


    protected $scene = [
        'create' => ['cnt_type', 'project_id', 'title', 'type_id'],
        'update' => ['cnt_id',],
        'statusTransfer' => ['cnt_id', 'status_enum_id'],
        'pageQuery' => ['project_id'],
        'associateTasks' => ['cnt_id', 'cnt_id_list','cnt_type'],
        'flowNodeAssociateTasks' => ['node_id'],
        'batchStatusTransfer' => ['cnt_ids', 'status_enum_id', 'update_empty_only'],
        'batchUpdate' => ['cnt_ids', 'field_list','cnt_type'],
        'batchUpdateCategory' => ['cnt_ids', 'type_id','status_enum_id'],
    ];


    protected $message = [
        'microservice_name.checkNameUnique' => '微服务名称重复',
        'title.checkEmoji' => '不可输入表情符号',
    ];

    /**
     * 验证名称是否唯一
     * @param $value
     * @param $rule
     * @param $data
     * @return bool
     * <AUTHOR>
     * @date 2024/7/8 上午10:16
     */
    protected function checkNameUnique($value, $rule, $data = [])
    {
        return MicroserviceModel::checkNameUnique($data['microservice_id'] ?? '', $data['microservice_name'] ?? '');
    }

    /**
     * 检查是否含有表情符号
     * @param $value
     * @return bool
     * <AUTHOR>
     * @date 2024/11/25 14:22
     */
    protected function checkEmoji($value)
    {
        return !$this->containsEmoji($value);
    }

    /**
     * 检查是否含有表情符号（从checkEmoji方法内抽出，避免重复申明）
     * @param $string
     * @return bool
     * User Long
     * Date 2025/4/7
     */
    private function containsEmoji($string) {
        $regex = '/[\x{1F600}-\x{1F64F}]|[\x{1F300}-\x{1F5FF}]|[\x{1F680}-\x{1F6FF}]|[\x{2600}-\x{26FF}]|[\x{2700}-\x{27BF}]/u';
        return preg_match($regex, $string) === 1;
    }

}
