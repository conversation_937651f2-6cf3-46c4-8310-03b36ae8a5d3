<?php
// 应用公共文件
use app\infrastructure\logic\EnumLogic;
use think\db\Query;

if (!function_exists('getPageSize')) {
    /**
     * 获取分页参数
     */
    function getPageSize(): array
    {
        $page = input('page/d', 1);
        $size = input('list_rows/d', 50);

        $page = max($page, 1);
        $size = max($size, 20);
        $size = min($size, 500);

        return ['page' => $page, 'list_rows' => $size];
    }
}

if (!function_exists('getCurrentMilliseconds')) {
    /**
     * 获取毫秒时间
     */
    function getCurrentMilliseconds(): float
    {
        list($microseconds, $seconds) = explode(' ', microtime());
        return round(($seconds . $microseconds) * 1000);
    }
}

if (!function_exists('mkdirUnmask')) {
    /**
     * 创建目录
     * @param $path
     * @param int $mod
     * @return void
     */
    function mkdirUnmask($path, int $mod = 0777): void
    {
        $old = umask(0);
        mkdir($path, $mod, true);
        umask($old);
    }
}

if (!function_exists('generateTree')) {
    /***
     * 无限极递归分类处理
     * @param $items
     * @return array
     * <AUTHOR>
     * @date 2023/7/14
     */
    function generateTree($items): array
    {
        $index_items = [];
        foreach ($items as $item) {
            $index_items[$item['id']] = $item;
        }
        $tree = [];
        foreach ($index_items as $item) {

            if (isset($index_items[$item['pid']])) {
                $index_items[$item['pid']]['child'][] = &$index_items[$item['id']];
            } else {
                $tree[] = &$index_items[$item['id']];
            }
        }
        return $tree;
    }
}

if (!function_exists('randStr')) {
    /**
     * 生成随机数
     * @param $len
     * @return array|int|string|string[]
     * <AUTHOR>
     * @date 2023/7/14 17:23
     * @noinspection PhpMissingParamTypeInspection
     */
    function randStr($len = 6): array|int|string
    {
        $chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789abcdefghijklmnopqrstuvwxyz';

        $string = time();

        for (; $len >= 1; $len--) {

            $position = rand() % strlen($chars);

            $position2 = rand() % strlen($string);

            $string = substr_replace($string, substr($chars, $position, 1), $position2, 0);

        }

        return $string;
    }
}

if (!function_exists('arrayShiftSelect')) {
    /**
     * 数组转select格式，支持一维，二维数组
     * 注意格式
     * [1 => "是",2 => "否"]
     * ['arr1'=> [1 => "是",2 => "否"], 'arr2'=> [1 => "是",2 => "否"]]
     * @param array $param
     * @param bool $is_total
     * @return array
     * User Long
     * Date 2023/4/6
     */
    function arrayShiftSelect(array $param = [], bool $is_total = false): array
    {
        $select = [];

        if (isMultiArray($param)) {
            foreach ($param as $k => $i) {
                // 等于 true 时加载 全部 选项
                if ($is_total) {
                    $select[$k][] = ['value' => '', 'label' => '全部'];
                }

                foreach ($i as $ke => $it) {
                    $select[$k][] = ['value' => $ke, 'label' => $it];
                }
            }
        } else {
            // 等于 true 时加载 全部 选项
            if ($is_total) {
                $select[] = ['value' => '', 'label' => '全部'];
            }

            foreach ($param as $ke => $it) {
                $select[] = ['value' => $ke, 'label' => $it];
            }
        }

        return $select;
    }
}

if (!function_exists('isMultiArray')) {
    /**
     * 判断是否多维数组
     * @param array $array
     * @return bool
     * <AUTHOR>
     * @date 2023/7/27 16:34
     */
    function isMultiArray(array $array = []): bool
    {
        if (is_array($array)) {
            $count = count($array, COUNT_RECURSIVE);
            return $count > count($array);
        }
        return false;
    }
}

if (!function_exists('arrayMultiUnique')) {
    /**
     * Desc: 多维数据去重
     * @param $array
     * @param $fields
     * @return array
     * @exception Exception
     * <AUTHOR>
     * @date 2023/10/31/16:12
     */
    function arrayMultiUnique($array, $fields): array
    {
        $result = [];

        foreach ($array as $item) {
            $key = '';

            foreach ($fields as $field) {
                $key .= $item[$field];
            }

            if (!isset($result[$key])) {
                $result[$key] = $item;
            }
        }

        return array_values($result);
    }
}

if (!function_exists('validateDate')) {
    /**
     * 验证时间格式
     * @param string $date 需验证时间
     * @param string $format 验证格式 默认 Y-m-d H:i:s
     * @return bool
     * User Long
     * Date 2022/12/27
     */
    function validateDate(string $date = '', string $format = 'Y-m-d H:i:s'): bool
    {
        $time = DateTime::createFromFormat($format, $date);

        return $time && $time->format($format) === $date;
    }
}

if (!function_exists('generateCopyName')) {
    /**
     * 生成复制名称,格式：被复制名称 +' copy' +'(递增数字)'
     * @param $name string 被复制的名称
     * @param $fieldName string 字段名称
     * @param $query Query  字段名称对应的模型查询
     * @return string
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2024/8/6 下午5:37
     */
    function generateCopyName($name, $fieldName, Query $query)
    {
        $copyName = $name . ' copy';
        $escaped = preg_quote($copyName);

        $dataList = $query->where([
            [$fieldName, 'like', "$name%"]
        ])->order($query->getPk().' desc')->select();
        //转义(被复制名称)+' copy'+(数字)?
        $reg = "/^$escaped(\([0-9]+\))?$/";
        $dataList = $dataList->filter(function ($q)use($reg,$fieldName){
            return preg_match($reg, $q[$fieldName]);
        });

        if ($dataList->count() > 0) {
            $maxIdData = $dataList->first();
            $pattern = '/\((\d+)\)$/';//匹配名称最后括号里的数字
            preg_match($pattern, $maxIdData->$fieldName, $matches);
            if ($matches[1] ?? null) {
                $count = ((int)$matches[1]) + 1;
                return $copyName . "($count)";
            } else {
                return $copyName . "(2)";
            }
        }
        return $copyName;
    }
}

if (!function_exists('getSystemEnumLibrary')) {
    /**
     * 获取枚举库数据
     * @param string $code
     * @return array
     * User Long
     * Date 2024/8/12
     */
    function getSystemEnumLibrary(string $code): array
    {
        $data = EnumLogic::selectorEnumData($code);

        $res = [];
        foreach ($data as $item) {
            $res[] = [
                'label' => $item['enum_name'],
                'value' => $item['enum_value']
            ];
        }

        return $res;
    }
}

if (!function_exists('unsetEmptyArray')) {
    /**
     * 删除空数组
     * @param array $array
     * @return array
     * User Long
     * Date 2024/11/20
     */
    function unsetEmptyArray(array $array = [])
    {
        $data = [];

        foreach ($array as $k => $v) {
            if (!empty($v)) $data[$k] = $v;
        }

        return $data;
    }
}

if (!function_exists('valueKeyConvert')) {
    /**
     * 值转键
     * 注意值是否唯一
     * @param array $array
     * @return array
     * User Long
     * Date 2024/12/5
     */
    function valueKeyConvert(array $array): array
    {

        $res = [];
        foreach ($array as $item) {
            $res[$item['value']] = $item['label'];
        }

        return $res;
    }
}

if (!function_exists('removeValueFromArray')) {
    /**
     * 移出数组内指定值
     * @param array $array
     * @param $valueToRemove
     * @return array
     * User Long
     * Date 2024/12/7
     */
    function removeValueFromArray(array $array, $valueToRemove): array
    {
        return array_filter($array, function($item) use ($valueToRemove) {
            return $item !== $valueToRemove;
        });
    }
}

if (!function_exists('md5Salt')) {
    /**
     * md5加盐
     * @param string $str 加密字符串
     * @param string $salt 需要加的盐
     * @return string
     * User Long
     * Date 2024/12/12
     */
    function md5Salt(string $str = '', string $salt = 'salting')
    {
        // 请输入需要加密字符
        if (!$str) throw new \exception\ParamsException('请输入需要加密字符');

        return md5(md5($str . '@#_' . $salt));
    }
}

if (!function_exists('spliceUserName')) {
    /**
     * 拼接用户名
     * @param string $enUserName 英文名
     * @param string $userName 中文名
     * @param string $positionName 主岗位
     * @return string
     * User Long
     * Date 2025/2/13
     */
    function spliceUserName(string $enUserName = '', string $userName = '', string $positionName = ''): string
    {
        $str = [];
        if (!empty($enUserName)) {
            $str[] = $enUserName;
        }
        if (!empty($userName)) {
            $str[] = $userName;
        }
        if (!empty($positionName)) {
            $str[] = $positionName;
        }

        return implode('.', $str);
    }
}

if (!function_exists('convertProjectId')) {
    /**
     * 转换项目ID
     * @param $projectId
     * @return array|int
     * User Long
     * Date 2025/2/25
     */
    function convertProjectId($projectId): int|array
    {
        return  is_array($projectId) ? $projectId : (int)$projectId;
    }
}

if (!function_exists('uniqueCoding')) {
    /**
     * 生成唯一code
     * @param string $prefix
     * @param int $machineId
     * @return string
     * User Long
     * Date 2025/3/10
     */
    function uniqueCoding(string $prefix = '', int $machineId = 1): string
    {
        $epoch    = 1654495724726; //开始时间,固定一个小于当前时间的毫秒数
        $max12bit = 4095; // 序列数 12字节
        $max41bit = 1099511627775; // 二进制的 毫秒级时间戳

        // 时间戳 42字节
        $time = floor(microtime(true) * 1000);
        // 当前时间 与 开始时间 差值
        $time -= $epoch;
        // 二进制的 毫秒级时间戳
        $base = decbin($max41bit + $time);
        // 机器id 10 字节
        if ($machineId) $machineId = str_pad(decbin($machineId), 10, "0", STR_PAD_LEFT);
        // 序列数 12字节
        $random = str_pad(decbin(mt_rand(0, $max12bit)), 12, "0", STR_PAD_LEFT);
        // 拼接
        $base = $base . $machineId . $random;
        // 转化为 十进制 返回
        $code = bindec($base);

        return $prefix . '_' . $code . mt_rand(10, 99) . mt_rand(10, 99);

    }
}

