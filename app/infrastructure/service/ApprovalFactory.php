<?php
/**
 * 审批策略工厂
 */

declare (strict_types=1);

namespace app\infrastructure\service;

use app\infrastructure\model\GeneralApprovalsModel;
use app\infrastructure\service\approval\IterationNodeApprovalStrategy;
use app\infrastructure\service\approval\WorkHoursApprovalStrategy;
use app\infrastructure\service\approval\WorkItemsApprovalStrategy;
use think\Exception;

/**
 * 审批策略工厂
 * 用于获取不同业务类型的审批策略
 */
class ApprovalFactory
{
    /**
     * 审批策略实例缓存
     * @var array
     */
    private static $strategies = [];

    /**
     * 获取审批策略
     * @param int $type 业务类型
     * @return ApprovalStrategy
     * @throws Exception
     */
    public static function getStrategy(int $type): ApprovalStrategy
    {
        if (isset(self::$strategies[$type])) {
            return self::$strategies[$type];
        }

        $strategy = null;
        switch ($type) {
            case GeneralApprovalsModel::TYPE_ITERATION_NODE:
                $strategy = new IterationNodeApprovalStrategy();
                break;
            case GeneralApprovalsModel::TYPE_WORK_ITEMS:
                $strategy = new WorkItemsApprovalStrategy();
                break;
            case GeneralApprovalsModel::TYPE_WORK_HOURS:
                $strategy = new WorkHoursApprovalStrategy();
                break;
            default:
                throw new Exception('不支持的业务类型');
        }

        self::$strategies[$type] = $strategy;
        return $strategy;
    }
} 