<?php
/**
 * 迭代节点审批策略
 */

declare (strict_types=1);

namespace app\infrastructure\service\approval;

use app\infrastructure\model\EnumModel;
use app\infrastructure\model\GeneralApprovalsModel;
use app\iterate\model\IterationModel;
use app\project\logic\IterationProcessNodeLogic;
use app\project\logic\ProjectUserLogic;
use app\project\model\IterationProcessNodeModel;
use think\Exception;

/**
 * 迭代节点审批策略
 * 处理迭代节点修改实际结束时间的审批
 */
class IterationNodeApprovalStrategy extends AbstractApprovalStrategy
{
    /**
     * 获取业务类型
     * @return int
     */
    public function getType(): int
    {
        return GeneralApprovalsModel::TYPE_ITERATION_NODE;
    }


    /**
     * 审批通过后的处理
     * @param  GeneralApprovalsModel  $model  审批模型
     * @return bool
     * @throws Exception
     */
    public function handleApproved(GeneralApprovalsModel $model): bool
    {
        try {
            $this->startTrans();

            if ( ! isset($model->content['end_time'])) {
                throw new Exception('审批内容格式错误');
            }
            // 获取原始数据的时分秒部分
//            $node = IterationProcessNodeModel::findById($model->business_model_id);

//            $originalTime = date('H:i:s', strtotime($node->getData('end_time')));

            // 合并新的日期和原始时间
            $newDateTime = date('Y-m-d H:i:s', strtotime($model->content['end_time']));

            IterationProcessNodeLogic::updateNode($model->business_model_id, [
                'end_time' => $newDateTime,
            ]);


            $this->commit();
            return true;
        } catch (\Throwable $e) {
            $this->rollback();
            throw $e;
        }
    }

    /**
     * 审批拒绝后的处理
     * @param  GeneralApprovalsModel  $model  审批模型
     * @return bool
     * @throws Exception
     */
    public function handleRejected(GeneralApprovalsModel $model): bool
    {
        try {
            $this->startTrans();

            // TODO: 实现迭代节点审批拒绝后的处理逻辑
            // 例如：发送通知给申请人

            $this->commit();
            return true;
        } catch (\Throwable $e) {
            $this->rollback();
            throw $e;
        }
    }

    /**
     * 获取审批人列表
     * @param  int  $id  主键ID
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getApprovers(int $id): array
    {
        $model = GeneralApprovalsModel::findById($id);
        // 获取迭代节点信息
        $iterationNode = IterationProcessNodeModel::findById($model->business_model_id);
        if ( ! $iterationNode) {
            return [];
        }
        // 获取迭代信息
        $iteration = IterationModel::findById($iterationNode->iteration_id);
        if ( ! $iteration) {
            return [];
        }

        $approvers = [];
        $extends = json_decode($iteration->extends ?? "{}", true);

        // 获取迭代负责人信息
        if ( ! empty($extends['iteration_leader'])) {
            $approvers = $this->getUserInfo($extends['iteration_leader']);
        }

        // 获取项目经理信息
        if ( ! empty($extends['project_manager'])) {
            $approvers = array_merge($approvers, $this->getUserInfo($extends['project_manager']));
        } else {
            $users = (new ProjectUserLogic())->selectorListQuery($iteration->project_id, '', '', [EnumModel::PROJECT_MANAGER]);
            if ($users) {
                $approvers = array_merge($approvers, $this->getUserInfo(array_column($users, 'user_id')));
            }
        }

        return array_unique($approvers, SORT_REGULAR);
    }

    /**
     * 验证发起人权限
     * @param  int  $userId           用户ID
     * @param  int  $businessModelId  业务模型ID
     * @return bool
     */
    public function validateInitiator(int $userId, int $businessModelId): bool
    {
        // 获取迭代节点信息
        $iterationNode = IterationProcessNodeModel::findById($businessModelId);
        if ( ! $iterationNode) {
            return false;
        }

        return in_array($userId, $iterationNode['node_data']['node_manger']['users'] ?? []);

    }
} 