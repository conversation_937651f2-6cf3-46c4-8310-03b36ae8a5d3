<?php
/**
 * 通用审批验证器
 */

namespace app\infrastructure\validate;

use think\Validate;

class GeneralApprovalsValidate extends Validate
{
    /**
     * 验证规则
     * @var array
     */
    protected $rule = [
        'general_approvals_id' => 'require|integer|gt:0',
        'is_audit' => 'require|in:0,1,2,3',
        'business_model_id' => 'require|integer|gt:0',
        'type' => 'require|in:1,2,3',
//        'content' => 'require'
    ];

    /**
     * 错误信息
     * @var array
     */
    protected $message = [
        'general_approvals_id.require' => 'ID不能为空',
        'general_approvals_id.integer' => 'ID必须为整数',
        'general_approvals_id.gt' => 'ID必须大于0',
        'is_audit.require' => '审批类型不能为空',
        'is_audit.in' => '审批类型只能是1、2或3',
        'business_model_id.require' => '业务模型ID不能为空',
        'business_model_id.integer' => '业务模型ID必须为整数',
        'business_model_id.gt' => '业务模型ID必须大于0',
        'type.require' => '业务类型不能为空',
        'type.in' => '业务类型只能是1、2或3',
//        'content.require' => '内容不能为空'
    ];

    /**
     * 验证场景
     * @var array
     */
    protected $scene = [
        'create' => ['business_model_id', 'type', 'content'],
        'update' => ['general_approvals_id', 'is_audit', 'content'],
        'delete' => ['general_approvals_id'],
        'detail' => ['general_approvals_id'],
        'approve' => ['general_approvals_id', 'is_audit', 'content'],
        'byBusinessModel' => ['business_model_id', 'type']
    ];
} 