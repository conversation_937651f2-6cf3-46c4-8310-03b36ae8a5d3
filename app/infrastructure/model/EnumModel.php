<?php
declare (strict_types=1);

namespace app\infrastructure\model;

use basic\BaseModel;

/**
 * This is the model class for table "t_enum".
 * @property int $enum_id id
 * @property string $is_delete 是否删除;1-是 0-否
 * @property string $enum_code 枚举code
 * @property string $enum_name 枚举名称
 * @property string $enum_value 枚举值
 * @property int $parent_id 上级id 0表示顶级
 * @property int $enum_type 枚举类型;1-系统 2-自定义
 */
class EnumModel extends BaseModel
{
    protected $name = 'enum';
    protected $pk = 'enum_id';

    const  TYPE_SYSTEM = 1;   //枚举类型;1-系统
    const  TYPE_CUSTOMER = 2; //枚举类型;2-自定义

    // 枚举库code管理
    public const SELECTOR_STATUS_TYPE = 'flowStatusType'; // 工作流-状态类型

    public const SYSTEM_ROLE = 'systemRole'; // 角色集合
    public const SOFTWARE_TEST = 'softwareTest'; // 软件测试
    public const BACKEND_DEVELOPMENT = 'backendDevelopment'; // 后端开发
    public const WEB_DEVELOP = 'webDevelop'; // 前端开发
    public const UI_DESIGNER = 'uiDesigner'; // UI设计师
    public const PRODUCT_MANAGER = 'productManager'; // 产品经理
    public const PROJECT_MANAGER = 'projectManager'; // 项目经理
    public const ITERATION_LEADER = 'iterationLeader'; // 迭代leader
    public const BUSINESS_ARCHITECT = 'businessArchitect'; // 业务架构师

    public const DEMAND_HANDLER = 'demandHandler'; // 需求工作流-处理人
    public const DEFECT_HANDLER = 'defectHandler'; // 缺陷工作流-处理人
    public const MAP_HANDLER = [
          self::DEMAND_HANDLER,
          self::DEFECT_HANDLER,
    ];

    public const PROJECT_ICON = 'projectIcon'; // 项目图标
    public const ITERATION_ICON = 'iterationIcon'; // 迭代图标
    public const PROJECT_STATUS_TYPE = 'projectStatusType'; // 项目状态
    public const PROJECT_PROCESS_STATUS = 1; // 进行中
    public const PROJECT_END_STATUS = 2; // 关闭

    const LIST_FIELDS = [
        'enum_id',
        'enum_code',
        'enum_name',
        'enum_value',
        'enum_type',
        'parent_id',
    ];

    public static function findById($id)
    {
        return static::status()->where(['enum_id' => $id])->find();
    }

    public static function findByCode($code)
    {

        $parent = static::status()->where(['enum_code' => $code])->find();
        if (!$parent) {
            return null;
        }

        $res = static::status()->where(['parent_id' => $parent['enum_id']])
            ->field(self::LIST_FIELDS)
            ->select();
        if ($res->isEmpty()) {
            return null;
        }

        return $res;

    }

    /**
     * 根据code查询指定配置
     * @param $code
     * User Long
     * Date 2024/8/12
     */
    public static function findEnumValueByCode($code)
    {
        return static::status()->where(['enum_code' => $code])->value('enum_value');
    }

    public function toDetail()
    {
        return $this->visible(self::LIST_FIELDS);
    }


}
