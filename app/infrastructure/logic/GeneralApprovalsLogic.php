<?php
/**
 * 通用审批逻辑层
 */

declare (strict_types=1);

namespace app\infrastructure\logic;

use app\infrastructure\model\GeneralApprovalsModel;
use app\infrastructure\service\ApprovalFactory;
use app\infrastructure\validate\GeneralApprovalsValidate;
use app\project\model\IterationProcessNodeModel;
use basic\BaseLogic;
use exception\NotFoundException;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\Exception;
use think\exception\ValidateException;
use think\Paginator;
use utils\Ctx;
use utils\DBTransaction;

class GeneralApprovalsLogic extends BaseLogic
{
    /**
     * 获取通用审批详情
     * @param  int  $id
     * @return array
     * @throws NotFoundException
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getDetail(int $id): array
    {
        $model = GeneralApprovalsModel::findById($id);
        if ( ! $model) {
            throw new NotFoundException('审批记录不存在');
        }

        $detail = $model->toDetail();
        // 添加审批人字段
        $detail['approvers'] = $this->getApprovers($model);

        return $detail;
    }

    /**
     * 根据业务模型ID和业务类型获取通用审批
     * @param  int  $businessModelId
     * @param  int  $type
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws NotFoundException
     */
    public function getByBusinessModelIdAndType(int $businessModelId, int $type): array
    {
        $model = GeneralApprovalsModel::findByBusinessModelIdAndType($businessModelId, $type);
        if ( ! $model) {
            throw new NotFoundException('审批记录不存在');
        }

        $detail = $model->toDetail();
        // 添加审批人字段
        $detail['approvers'] = $this->getApprovers($model);

        return $detail;
    }

    /**
     * 分页查询通用审批
     * @param  array  $params
     * @return Paginator
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function pageQuery(array $params): Paginator
    {

        $query = GeneralApprovalsModel::where('is_delete', GeneralApprovalsModel::DELETE_NO);
        // 业务类型筛选
        if (isset($params['type'])
            && \in_array($params['type'], [
                GeneralApprovalsModel::TYPE_ITERATION_NODE,
                GeneralApprovalsModel::TYPE_WORK_ITEMS,
                GeneralApprovalsModel::TYPE_WORK_HOURS
            ])
        ) {
            $query->where('type', \intval($params['type']));
        }

        // 审批类型筛选
        if (isset($params['is_audit'])
            && \in_array($params['is_audit'], [
                GeneralApprovalsModel::AUDIT_INITIATE,
                GeneralApprovalsModel::AUDIT_AGREE,
                GeneralApprovalsModel::AUDIT_REJECT
            ])
        ) {
            $query->where('is_audit', \intval($params['is_audit']));
        }

        // 业务模型ID筛选
        if (isset($params['business_model_id']) && $params['business_model_id'] > 0) {
            $query->where('business_model_id', \intval($params['business_model_id']));
        }

        $paginator = $query->order('general_approvals_id', 'desc')
            ->paginate(\getPageSize());

        // 为每条记录添加审批人字段
        $items = $paginator->items();
        foreach ($items as &$item) {
            $detail = $item->toArray();
            $detail['approvers'] = $this->getApprovers($item);
            $item->setAttr('approvers', $detail['approvers']);
        }

        return $paginator;
    }

    /**
     * 创建通用审批
     * @param  array  $data
     * @return GeneralApprovalsModel
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws ValidateException
     * @throws Exception
     */
    public function create(array $data): GeneralApprovalsModel
    {
        // 验证数据
        \validate(GeneralApprovalsValidate::class)
            ->scene('create')
            ->check($data);

        // 检查业务类型是否支持
        if ( ! \in_array($data['type'], [
            GeneralApprovalsModel::TYPE_ITERATION_NODE,
            GeneralApprovalsModel::TYPE_WORK_ITEMS,
            GeneralApprovalsModel::TYPE_WORK_HOURS
        ])
        ) {
            throw new ValidateException('不支持的业务类型');
        }

        // 针对迭代节点修改实际结束时间的特殊验证
        if ($data['type'] == GeneralApprovalsModel::TYPE_ITERATION_NODE) {
            // 确保内容字段中包含所需数据
            if (empty($data['content']) || ! isset($data['content']['end_time'])) {
                throw new ValidateException('迭代节点修改缺少必要的时间信息');
            }
            $startTime = IterationProcessNodeModel::findById($data['business_model_id'])->getdata('start_time');
            if ( ! $startTime) {
                throw new ValidateException('迭代开始时间不存在');
            }
            // 获取结束时间和原开始时间
            $endTime = strtotime(date('Y-m-d H:i:s', strtotime($data['content']['end_time'])));
            $sourceStartTime = strtotime(date('Y-m-d H:i:s', strtotime($startTime)));
            // 确保结束时间不小于原开始时间
            if ($endTime < $sourceStartTime) {
                throw new ValidateException('修改的实际结束时间不能小于原开始时间');
            }
        }

        // 获取策略类并验证发起人权限
        $strategy = ApprovalFactory::getStrategy($data['type']);
        if ( ! $strategy->validateInitiator(Ctx::$userId, $data['business_model_id'])) {
            $errorMessages = [
                GeneralApprovalsModel::TYPE_ITERATION_NODE => '仅节点负责人才可发起',
                GeneralApprovalsModel::TYPE_WORK_ITEMS     => '仅架构师才可发起',
                GeneralApprovalsModel::TYPE_WORK_HOURS     => '仅架构师才可发起'
            ];
            throw new ValidateException($errorMessages[$data['type']] ?? '您没有权限发起此审批');
        }

        // 检查是否已存在审批记录
        $existApproval = GeneralApprovalsModel::findByBusinessModelIdAndType($data['business_model_id'], $data['type']);

        // 对于type=2（需求变更）的审批，不限制必须结束才可发起
        // 对于其他类型，只有当存在审批记录且状态为"发起"（即审批中）时，才不允许重新发起
        if ($data['type'] != GeneralApprovalsModel::TYPE_WORK_ITEMS
            && $existApproval
            && $existApproval->is_audit == GeneralApprovalsModel::AUDIT_INITIATE
        ) {
            throw new ValidateException('已存在审批中的记录，请等待审批完成后再发起');
        }


        try {
            DBTransaction::begin();

            // 创建审批记录
            $model = new GeneralApprovalsModel();
            $model->save([
                'is_audit'          => GeneralApprovalsModel::AUDIT_INITIATE,
                'business_model_id' => $data['business_model_id'],
                'type'              => $data['type'],
                'content'           => $data['content'],
            ]);

            // 检查发起人是否也是审批人，如果是则直接审批通过
            $approvers = $strategy->getApprovers($model->general_approvals_id);
            $approverIds = array_column($approvers, 'user_id');

            // 当前用户是审批人之一，直接通过审批
            if (in_array(Ctx::$userId, $approverIds)) {
                // 更新审批状态为同意
                $model->save([
                    'is_audit' => GeneralApprovalsModel::AUDIT_AGREE,
                ]);

                // 执行审批通过后的处理
                $strategy->handleApproved($model);
            }

            DBTransaction::commit();
            return $model;
        } catch (\Throwable $e) {
            DBTransaction::rollback();
            throw $e;
        }
    }

    /**
     * 审批
     * @param  int    $id
     * @param  int    $isAudit
     * @param  array  $content
     * @return bool
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws NotFoundException
     * @throws ValidateException
     * @throws Exception
     */
    public function approve(int $id, int $isAudit, array $content = []): bool
    {
        // 验证审批类型
        if ( ! in_array($isAudit, [GeneralApprovalsModel::AUDIT_AGREE, GeneralApprovalsModel::AUDIT_REJECT])) {
            throw new ValidateException('审批类型错误');
        }

        // 查找审批记录
        $model = GeneralApprovalsModel::findById($id);
        if ( ! $model) {
            throw new NotFoundException('审批记录不存在');
        }

        // 检查审批状态
        if ($model->is_audit != GeneralApprovalsModel::AUDIT_INITIATE) {
            throw new ValidateException('审批记录状态错误');
        }

        // 验证审批人权限
        $strategy = ApprovalFactory::getStrategy($model->type);
        if ( ! $strategy->validateApprover(Ctx::$userId, $model->general_approvals_id)) {
            $errorMessages = [
                GeneralApprovalsModel::TYPE_ITERATION_NODE => '仅“迭代leader、项目经理”进行审批',
                GeneralApprovalsModel::TYPE_WORK_ITEMS     => '仅可所选择的需求“负责人”进行审批',
                GeneralApprovalsModel::TYPE_WORK_HOURS     => '仅可任务的“处理人”进行审批'
            ];
            throw new ValidateException($errorMessages[$model->type] ?? '没有审批权限');
        }

        try {
            DBTransaction::begin();

            // 更新审批记录
            $updateData = [
                'is_audit' => $isAudit,
            ];

            // 如果有新的内容，则更新内容
            if ( ! empty($content)) {
                $oldContent = $model->content;
                $updateData['content'] = array_merge($oldContent, $content);
            }

            $model->save($updateData);

            // 根据审批结果执行相应的处理
            if ($isAudit == GeneralApprovalsModel::AUDIT_AGREE) {
                $strategy->handleApproved($model);
            } else {
                $strategy->handleRejected($model);
            }

            DBTransaction::commit();
            return true;
        } catch (\Throwable $e) {
            DBTransaction::rollback();
            throw $e;
        }
    }

    /**
     * 删除通用审批
     * @param  int  $id
     * @return bool
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws NotFoundException
     * @throws ValidateException
     */
    public function delete(int $id): bool
    {
        // 验证数据
        validate(GeneralApprovalsValidate::class)->scene('delete')->check(['general_approvals_id' => $id]);

        // 查找记录
        $model = GeneralApprovalsModel::findById($id);
        if ( ! $model) {
            throw new NotFoundException('审批记录不存在');
        }

        // 删除记录
        return $model->save([
            'is_delete' => GeneralApprovalsModel::DELETE_YES,
        ]);
    }

    /**
     * 获取审批人信息
     * @param  GeneralApprovalsModel  $model  审批记录模型
     * @return array 审批人信息数组
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getApprovers(GeneralApprovalsModel $model): array
    {
        // 获取对应的策略类
        $strategy = ApprovalFactory::getStrategy($model->type);

        // 调用策略类的getApproversByAuditStatus方法
        return $strategy->getApproversByAuditStatus($model);
    }
} 