<?php
/**
 * Desc 示例 - 逻辑层
 * User
 * Date 2024/07/03
 */

declare (strict_types=1);

namespace app\infrastructure\logic;

use app\infrastructure\model\FieldConfigModel;
use app\infrastructure\model\FieldSubsetModel;
use app\infrastructure\validate\FieldValidate;
use app\project\logic\ProjectInfoLogic;
use app\project\logic\ProjectTemplateLogic;
use app\project\model\ProjectModel;
use app\project\model\ProjectTemplateModel;
use app\work_items\model\WorkItemsModel;
use basic\BaseLogic;
use basic\BaseModel;
use exception\BusinessException;
use exception\NotFoundException;
use exception\ParamsException;
use think\Collection;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use utils\DBTransaction;


class FieldConfigLogic extends BaseLogic
{
    // 重命名链接
    private const RENAME_URL = '/fieldConfig/rename';

    /**
     * 校验名称是否与模板相同
     * @param $projectInfo
     * @param string $fieldLabel
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function validateProjectFieldData($projectInfo, string $fieldLabel = ''): void
    {
        if ($projectInfo) {
            if ($projectInfo->is_template == ProjectTemplateModel::IS_TEMPLATE_NO) {
                $templateId = ProjectTemplateLogic::getProjectIdByProjectTemplateId($projectInfo->project_template_id);

                if ($templateId) {
                    $fieldLabelExist = FieldConfigModel::findFieldAndName($templateId, $fieldLabel);
                    if ($fieldLabelExist) {
                        throw new ParamsException(ProjectTemplateLogic::getProductNameByProductId($projectInfo->project_template_id) . '-字段名称已存在');
                    }
                }
            } else {
                $projectIds = ProjectInfoLogic::getProjectIdsByTemplateId($projectInfo->project_template_id)
                    ->where('is_template',  ProjectTemplateModel::IS_TEMPLATE_NO);
                $fieldConfigNameExist = FieldConfigModel::status()
                    ->with(['project' => function($sql) {
                        $sql->bind(['project_name']);
                    }])
                    ->whereIn('project_id', $projectIds->column('project_id'))
                    ->select();

                if (!$fieldConfigNameExist->where('field_label', $fieldLabel)->isEmpty()) {
                    throw new ParamsException('【'.implode('】、【', $fieldConfigNameExist->where('field_label', $fieldLabel)->column('project_name')).'】-字段名称已存在');
                }
            }
        }
    }

    /**
     * 新增
     * @param $params
     * @return FieldConfigModel
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date   2024/7/9 下午4:01
     */
    public function create($params)
    {

        validate(FieldValidate::class)->scene('create')->check($params);

        $params['field_type'] = FieldConfigModel::FIELD_TYPE_CUSTOMIZE;
        $params['field_name'] = FieldConfigModel::getNextFieldName();

        if ( ! ($params['field_component']['componentType'] ?? null)) {
            throw new ParamsException('组件中缺少组件类型：componentType');
        }
        $params['component_type'] = $params['field_component']['componentType'];


        $model = new FieldConfigModel();
        $model->save($params);

        return $model->toDetail();
    }

    /**
     * 删除
     * @param $fieldId
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date   2024/7/9 下午4:01
     */
    public function delete($fieldId)
    {
        $model = FieldConfigModel::findById($fieldId);
        if ( ! $model) {
            throw new NotFoundException();
        }
        $model->deleteField();
        $model->save();
        $model->deleteCache();
    }

    /**
     * 修改
     * @param $params
     * @return FieldConfigModel|array|mixed|\think\Model
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date   2024/7/9 下午4:01
     */
    public function update($params)
    {

        validate(FieldValidate::class)->scene('update')->check($params);
        $model = FieldConfigModel::findById($params['field_id']);
        if ( ! $model) {
            throw new NotFoundException();
        }

        $model->save($params);
        $model->deleteCache();

        return $model;
    }

    /**
     * 详情
     * @param $fieldId
     * @return FieldConfigModel
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date   2024/7/9 下午4:01
     */
    public function detail($fieldId)
    {
        $model = FieldConfigModel::findById($fieldId);
        if ( ! $model) {
            throw new NotFoundException();
        }

        return $model->toDetail();
    }

    /**
     * 分页查询
     * @param $params
     * @return \think\Paginator
     * @throws \think\db\exception\DbException
     * <AUTHOR>
     * @date   2024/7/9 下午4:01
     */
    public function pageQuery($params)
    {
        validate(FieldValidate::class)->scene('getListByModuleIdGroupByFieldType')->check($params);

        $where = [
            ['is_delete', '=', BaseModel::DELETE_NOT],
            ['module_id', '=', $params['module_id']],
        ];

        if (FieldConfigModel::isRequireProjectId($params['module_id'])) {
            $where[] = ['project_id', 'in', [FieldConfigModel::PROJECT_NOT_HAS, $params['project_id']]];
        }

        return (new FieldConfigModel)
            ->where($where)
            ->field(FieldConfigModel::LIST_FIELDS)->order('field_type asc,field_id asc')
            ->paginate(getPageSize());

    }

    /**
     * 获取指定模块的所有数据并且按照field_type分类
     * @param $params  =[
     *                 "moduleId"=>'',
     *                 "allowSetting"=>allow_setting是否允许配置，不传查询所,
     *                 "projectId"=>所属项目id,
     *                 ]
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date   2024/7/10 上午11:28
     */
    public static function getListByModuleIdGroupByFieldType($params)
    {
        return self::resultClassification(self::getListByModuleId($params));
    }

    /**
     * 获取指定模块的所有数据
     * @param $params  =[
     *                 "moduleId"=>'',
     *                 "allowSetting"=>allow_setting是否允许配置，不传查询所,
     *                 "projectId"=>所属项目id,
     *                 ]
     * @return Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date   2024/7/10 上午11:28
     */
    public static function getListByModuleId($params)
    {

        validate(FieldValidate::class)->scene('getListByModuleIdGroupByFieldType')->check($params);

        $where = [
            ['module_id', '=', $params['module_id']],
            ['is_delete', '=', BaseModel::DELETE_NOT],
        ];


        if (FieldConfigModel::isRequireProjectId($params['module_id'])) {
            if ( ! is_array($params['project_id'])) {
                $params['project_id'] = [$params['project_id']];
            }

            // 获取模板项目
            $projectInfo = ProjectInfoLogic::getProjectDataSet($params['project_id'])->where('is_template', 0);
            if (!$projectInfo->isEmpty()) {
                $templateIds = ProjectTemplateLogic::getProjectIdsByProjectTemplateIds($projectInfo->column('project_template_id'));

                $params['project_id'] = array_values(array_unique(array_merge($params['project_id'], $templateIds)));
            }

            $where[] = ['project_id', 'in', array_merge([FieldConfigModel::PROJECT_NOT_HAS], $params['project_id'])];
        }

        if ($params['allow_setting'] ?? null) {
            $where[] = ['allow_setting', '=', $params['allow_setting']];
        }

        if ($params['isSearch'] ?? null) {
            $where[] = ['component_type', '<>', FieldConfigModel::COMPONENT_TYPE_EDITOR];
        }


        return FieldConfigModel::where($where)
            ->field(FieldConfigModel::LIST_FIELDS)->select();
    }

    /**
     * 获取需求、任务、缺陷、以及其中的部分公共字段的汇总
     * @param $params
     * @return array[]
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * <AUTHOR>
     * @date   2024/11/7 14:47
     */
    public static function getDemandTaskFlawSummaryFieldList($params)
    {

        if ( ! ($params['project_id'] ?? false)) {
            throw new ParamsException("项目id不可为空");
        }

        if ( ! is_array($params['project_id'])) {
            $params['project_id'] = [$params['project_id']];
        }


        $where = [
            [
                'module_id', 'in', [
                FieldConfigModel::MODULE_TYPE_REQUIREMENT,
                FieldConfigModel::MODULE_TYPE_TASK,
                FieldConfigModel::MODULE_TYPE_DEFECT,
            ]
            ],
            ['is_delete', '=', BaseModel::DELETE_NOT],
            ['project_id', 'in', array_merge([FieldConfigModel::PROJECT_NOT_HAS], $params['project_id'])],
        ];


        if ($params['allow_setting'] ?? null) {
            $where[] = ['allow_setting', '=', $params['allow_setting']];
        }

        if ($params['isSearch'] ?? null) {
            $where[] = ['component_type', '<>', FieldConfigModel::COMPONENT_TYPE_EDITOR];
        }

        $fieldList = FieldConfigModel::where($where)
            ->field(FieldConfigModel::LIST_FIELDS)->select();
        //公共字段
        $pubFieldList = $fieldList->where('module_id', '=', FieldConfigModel::MODULE_TYPE_REQUIREMENT)
            ->where('field_name', 'in', [
                'cnt_id',
                'title',
                'iteration_id',
                'handler_uid',
                'update_at',
                'create_by',
                'create_at',
                'estimated_work_hours',
                'actual_work_hours',
                'remaining_work',
                'exceeding_working_hours',
                'speed_of_progress',
            ])->each(function ($v) {
                $v['module_id'] = 0;
            });
        $demandFieldList = $fieldList->where('module_id', '=', FieldConfigModel::MODULE_TYPE_REQUIREMENT);
        $taskFieldList = $fieldList->where('module_id', '=', FieldConfigModel::MODULE_TYPE_TASK);
        $flawFieldList = $fieldList->where('module_id', '=', FieldConfigModel::MODULE_TYPE_DEFECT);

        return [
            [
                'label'       => '公共字段',
                'cnt_type'    => 0,
                'field_class' => self::resultClassification($pubFieldList),
            ],
            [
                'label'       => '需求',
                'cnt_type'    => WorkItemsModel::CNT_TYPE_DEMAND,
                'field_class' => self::resultClassification($demandFieldList),
            ],
            [
                'label'       => '任务',
                'cnt_type'    => WorkItemsModel::CNT_TYPE_TASK,
                'field_class' => self::resultClassification($taskFieldList),
            ],
            [
                'label'       => '缺陷',
                'cnt_type'    => WorkItemsModel::CNT_TYPE_FLAW,
                'field_class' => self::resultClassification($flawFieldList),
            ],
        ];
    }

    /**
     * 需求、任务、缺陷字段合并去重
     * @param $params
     * @return array[]
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * <AUTHOR>
     * @date   2024/11/7 14:47
     */
    public static function getDemandTaskFlawSummaryDeduplicationFieldList($params)
    {

        if ( ! ($params['project_id'] ?? false)) {
            throw new ParamsException("项目id不可为空");
        }

        if ( ! is_array($params['project_id'])) {
            $params['project_id'] = [$params['project_id']];
        }

        $where = [
            [
                'module_id', 'in', [
                FieldConfigModel::MODULE_TYPE_REQUIREMENT,
                FieldConfigModel::MODULE_TYPE_TASK,
                FieldConfigModel::MODULE_TYPE_DEFECT,
            ]
            ],
            ['is_delete', '=', BaseModel::DELETE_NOT],
            ['project_id', 'in', array_merge([FieldConfigModel::PROJECT_NOT_HAS], $params['project_id'])],
        ];


        if ($params['allow_setting'] ?? null) {
            $where[] = ['allow_setting', '=', $params['allow_setting']];
        }

        if ($params['isSearch'] ?? null) {
            $where[] = ['component_type', '<>', FieldConfigModel::COMPONENT_TYPE_EDITOR];
        }

        $fieldList = FieldConfigModel::where($where)
            ->field(FieldConfigModel::LIST_FIELDS)->select()->toArray();


        $deduplication = [];
        $summary = [];
        foreach ($fieldList as $field) {
            $deduplication[$field['field_name']] = $field;
            $summary[$field['field_name']][self::moduleIdToCntType($field['module_id'])] = $field;
        }
        foreach ($deduplication as &$v) {
            $v['summary'] = $summary[$v['field_name']];
        }

        return self::resultClassification(new Collection($deduplication));

    }

    private static function moduleIdToCntType($moduleId)
    {
        return match ($moduleId) {
            FieldConfigModel::MODULE_TYPE_REQUIREMENT => WorkItemsModel::CNT_TYPE_DEMAND,
            FieldConfigModel::MODULE_TYPE_TASK => WorkItemsModel::CNT_TYPE_TASK,
            FieldConfigModel::MODULE_TYPE_DEFECT => WorkItemsModel::CNT_TYPE_FLAW,
            default => throw new ParamsException()
        };
    }

    /**
     * 通过子集标识获取字段集
     * @param $params
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * <AUTHOR>
     * @date   2024/9/29 10:37
     */
    public static function getListBySubKeyGroupByFieldType($params)
    {

        validate(FieldValidate::class)->scene('getListBySubKeyGroupByFieldType')->check($params);


        return self::resultClassification(self::getListBySubKey($params['sub_key'], $params['project_id'] ?? '', $params['is_search'] ?? 0));
    }

    /**
     * 通过子集标识获取字段集
     * @param $subKek
     * @param $projectId
     * @param $isSearch
     * @return FieldConfigModel[]|array|Collection
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * <AUTHOR>
     * @date   2024/12/13 下午5:10
     */
    public static function getListBySubKey($subKek, $projectId = '', $isSearch = 0)
    {


        $subSet = FieldSubsetModel::getByKey($subKek);
        if ( ! $subSet) {
            throw new NotFoundException();
        }

        $where = [
            ['is_delete', '=', BaseModel::DELETE_NOT],
            ['module_id', 'in', $subSet->module_id]
        ];

        $whereOr = [
            ['field_name', 'in', array_column($subSet->field_list, 'field_name')]
        ];

        if ($subSet->include_custom) {
            $whereOr[] = ['field_type', '=', FieldConfigModel::FIELD_TYPE_CUSTOMIZE];
        }

        if ($projectId) {
            if ( ! is_array($projectId)) {
                $projectId = [$projectId];
            }
            $where[] = ['project_id', 'in', array_merge([FieldConfigModel::PROJECT_NOT_HAS], $projectId)];
        }

        if ($isSearch) {
            $where[] = ['component_type', '<>', FieldConfigModel::COMPONENT_TYPE_EDITOR];
        }


        return FieldConfigModel::where($where)
            ->where(function ($query) use ($whereOr) {
                $query->whereOr($whereOr);
            })
            ->field(FieldConfigModel::LIST_FIELDS)->select();
    }

    /**
     * 将字段集分类
     * @param $moduleId
     * @param $fieldList
     * @return array
     * <AUTHOR>
     * @date   2024/9/29 10:15
     */
    public static function resultClassification(Collection $fieldList)
    {

        $result = [];
        //category_id有值代表需要分类
        if (array_sum($fieldList->column('category_id')) > 0) {
            $result[] = [
                'name'        => 'base',
                'label'       => '基础字段',
                'is_customer' => 0,
                'field_list'  => $fieldList->where('field_type', '=', FieldConfigModel::FIELD_TYPE_SYSTEM)
                    ->where('category_id', '=', FieldConfigModel::CATEGORY_BASE)
                    ->values()->toArray()
            ];
            $result[] = [
                'name'        => 'user_date',
                'label'       => '人员与时间字段',
                'is_customer' => 0,
                'field_list'  => $fieldList->where('field_type', '=', FieldConfigModel::FIELD_TYPE_SYSTEM)
                    ->where('category_id', '=', FieldConfigModel::CATEGORY_USER_DATE)
                    ->values()->toArray()
            ];
            $result[] = [
                'name'        => 'work_hours',
                'label'       => '工时字段',
                'is_customer' => 0,
                'field_list'  => $fieldList->where('field_type', '=', FieldConfigModel::FIELD_TYPE_SYSTEM)
                    ->where('category_id', '=', FieldConfigModel::CATEGORY_WORK_HOURS)
                    ->values()->toArray()
            ];
            $result[] = [
                'name'        => 'customer',
                'label'       => '自定义字段',
                'is_customer' => 1,
                'field_list'  => $fieldList->where('field_type', '=', FieldConfigModel::FIELD_TYPE_CUSTOMIZE)->values()->toArray()
            ];

        } else {
            $result[] = [
                'name'        => 'sys',
                'label'       => '系统字段',
                'is_customer' => 0,
                'field_list'  => $fieldList->where('field_type', '=', FieldConfigModel::FIELD_TYPE_SYSTEM)->values()->toArray()
            ];
            $result[] = [
                'name'        => 'customer',
                'label'       => '自定义字段',
                'is_customer' => 1,
                'field_list'  => $fieldList->where('field_type', '=', FieldConfigModel::FIELD_TYPE_CUSTOMIZE)->values()->toArray()
            ];
        }

        return $result;
    }

    /**
     * 根据fieldName获取
     * @param $params
     * @return FieldConfigModel[]|array|Collection
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * <AUTHOR>
     * @date   2024/8/31 15:46
     */
    public function getListByFieldName($params)
    {

        validate(FieldValidate::class)->scene('getListByModuleIdGroupByFieldType')->check($params);

        $where = [
            ['module_id', '=', $params['module_id']],
            ['is_delete', '=', BaseModel::DELETE_NOT],
        ];
        if ($params['field_name'] ?? false) {
            $where[] = ['field_name', 'in', $params['field_name']];
        }

        if (FieldConfigModel::isRequireProjectId($params['module_id'])) {
            $where[] = ['project_id', 'in', [FieldConfigModel::PROJECT_NOT_HAS, $params['project_id']]];
        }

        return FieldConfigModel::where($where)
            ->field(FieldConfigModel::LIST_FIELDS)
//            ->order('field_sort desc')
            ->select();
    }

    /**
     * 获取指定模块的固定字段
     * @param $moduleId
     * @return FieldConfigModel[]|array|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date   2024/7/19 下午2:16
     */
    public function getFixedList($moduleId)
    {
        return (new FieldConfigModel)
            ->where(['is_delete' => BaseModel::DELETE_NOT, 'module_id' => $moduleId, 'template_default' => FieldConfigModel::TEMPLATE_DEFAULT_YES])
            ->field(FieldConfigModel::LIST_FIELDS)
            ->order('field_sort desc')
            ->select();
    }

    /**
     * 复制字段管理
     * @param  int  $oldProjectId
     * @param  int  $newProjectId
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/10/14
     */
    public static function copyProjectDataByProjectId(int $oldProjectId, int $newProjectId)
    {
        $oldProjectData = FieldConfigModel::status()->where([
            'project_id' => $oldProjectId,
            'field_type' => FieldConfigModel::FIELD_TYPE_CUSTOMIZE
        ])->select();

        foreach ($oldProjectData as $oldProjectDatum) {
            $model = new FieldConfigModel();
            $model->save([
                'field_type'       => $oldProjectDatum->field_type,
                'remark'           => $oldProjectDatum->remark,
                'field_label'      => $oldProjectDatum->field_label,
                'field_name'       => $oldProjectDatum->field_name,
                'field_component'  => $oldProjectDatum->field_component,
                'field_sort'       => $oldProjectDatum->field_sort,
                'module_id'        => $oldProjectDatum->module_id,
                'project_id'       => $newProjectId,
                'category_id'      => $oldProjectDatum->category_id,
                'allow_setting'    => $oldProjectDatum->allow_setting,
                'is_edit'          => $oldProjectDatum->is_edit,
                'template_default' => $oldProjectDatum->template_default,
                'component_type'   => $oldProjectDatum->component_type
            ]);
        }
    }

    /**
     * 根据 模块id 字段名  获取组件内容
     * @param  int    $moduleId
     * @param  array  $fieldName
     * @return array
     * User Long
     * Date 2024/11/7
     */
    public static function columnFieldComponentByFieldName(int $moduleId, array $fieldName = [])
    {
        return FieldConfigModel::columnFieldComponentByFieldName($moduleId, $fieldName);
    }

    /**
     * 复制至 功能增加自定义字段
     * @param int $moduleId
     * @param int $oldProjectId
     * @param int $newProjectId
     * @param array $fieldContent
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2025/4/12
     */
    public static function copyCustomizeFieldFromCategory(int $moduleId, int $oldProjectId, int $newProjectId, array $fieldContent = [])
    {
        $oldData = FieldConfigModel::status()
            ->where(['module_id' => $moduleId, 'field_type' => FieldConfigModel::FIELD_TYPE_CUSTOMIZE, 'project_id' => $oldProjectId])
            ->whereIn('field_name', array_column($fieldContent, 'field_name'))
            ->select();

        $diffData = FieldConfigModel::status()->where([
            'project_id' => $newProjectId,
        ])
            ->whereIn('field_label', array_column($oldData->toArray(), 'field_label'))
            ->select();

        $res = ['data' => [], 'is_rename' => false];
        foreach ($oldData as $oldDatum) {
            foreach ($fieldContent as &$item) {
                if ($item['field_name'] == $oldDatum['field_name']) {
                    $model = new FieldConfigModel();
                    $rename = $oldDatum['field_label'];
                    if (!$diffData->where('field_label', $rename)->isEmpty()) {
                        $firstDiffData = $diffData->where('field_label', $oldDatum['field_label'])->first();
                        if (!$res['is_rename']) {
                            $res['is_rename'] = true;
                        }
                        $rename = FieldConfigModel::generateName($firstDiffData->field_label, $firstDiffData->project_id);
                    }
                    $data = [
                        'field_type'       => $oldDatum->field_type,
                        'remark'           => $oldDatum->remark,
                        'field_label'      => $rename,
                        'field_name'       => FieldConfigModel::getNextFieldName(),
                        'field_component'  => $oldDatum->field_component,
                        'field_sort'       => $oldDatum->field_sort,
                        'module_id'        => $oldDatum->module_id,
                        'project_id'       => $newProjectId,
                        'category_id'      => $oldDatum->category_id,
                        'allow_setting'    => $oldDatum->allow_setting,
                        'is_edit'          => $oldDatum->is_edit,
                        'template_default' => $oldDatum->template_default,
                        'component_type'   => $oldDatum->component_type
                    ];
                    $model->save($data);

                    if (isset($firstDiffData)) {
                        $res['data']['field_config'][] = ['field_id' => $model->field_id, 'field_label' => $rename, 'project_id' => $newProjectId, 'url' => self::RENAME_URL];
                    }
                    // 修改新的唯一标识
                    $item['field_name'] = $data['field_name'];
                }
            }
        }

        $res['field_content'] = $fieldContent;

        return $res;
    }

    /**
     * 获取复制至项目/模板数据
     * @param int $fieldId
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2025/4/12
     */
    public function getCopyToSelector(int $fieldId)
    {
        $model = FieldConfigModel::findById($fieldId);

        if (!$model) {
            throw new NotFoundException();
        }

        if ($model->field_type == FieldConfigModel::FIELD_TYPE_SYSTEM) {
            throw new BusinessException('仅支持自定义字段复制！');
        }

        $res = [];

        $projectInfo = ProjectInfoLogic::getProjectData($model->project_id);
        if (!$projectInfo) {
            return $res;
        }

        // 根据是否模板，查询对应下啦数据
        switch ($projectInfo->is_template) {
            case ProjectModel::IS_TEMPLATE_YES:
                $res['data'] = (new ProjectTemplateLogic())->getTemplateSelector([
                    ['project_template_id', '!=', $projectInfo->project_template_id]
                ]);
                break;
            case ProjectModel::IS_TEMPLATE_NOT:
                $res['data'] = (new ProjectInfoLogic())->getProjectSelector([
                    ['project_id', '!=', $projectInfo->project_id],
//                    ['project_template_id', '=', $projectInfo->project_template_id]
                ]);
                break;
        }

        $res['is_template'] = $projectInfo->is_template;

        return $res;
    }

    /**
     * 自定义字段复制至项目/模板
     * @param int $fieldId
     * @param array $copyToIds
     * @param int $isTemplate
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2025/4/12
     */
    public function categoryCopyTo(int $fieldId, array $copyToIds, int $isTemplate)
    {
        $oldField = FieldConfigModel::findById($fieldId);

        if (!$oldField) {
            throw new NotFoundException();
        }
        $oldField = $oldField->toArray();

        $newProjectIds = [];
        $newName = null;
        switch ($isTemplate) {
            case ProjectModel::IS_TEMPLATE_YES:
                $newProjectIds = ProjectTemplateLogic::getProjectIdsByProjectTemplateIds($copyToIds);
                $newName = ProjectTemplateLogic::getTemplateNameByProjetIds($copyToIds);
                break;
            case ProjectModel::IS_TEMPLATE_NOT:
                $newProjectIds = $copyToIds;
                $newName = ProjectInfoLogic::getProjetNameByProjetIds($copyToIds);
                break;
        }

        $res = ['data' => [], 'is_rename' => false];

        $diffData = FieldConfigModel::status()->whereIn('project_id', $newProjectIds)->where(['field_label' => $oldField['field_label']])->column('field_label, project_id', 'project_id');

        try {
            DBTransaction::begin();
            foreach ($newProjectIds as $newProjectId) {
                $model = new FieldConfigModel();
                $rename = $oldField['field_label'];
                if (isset($diffData[$newProjectId])) {
                    if (!$res['is_rename']) {
                        $res['is_rename'] = true;
                    }
                    $rename = FieldConfigModel::generateName($diffData[$newProjectId]['field_label'], $diffData[$newProjectId]['project_id']);
                }
                $data = [
                    'field_type'       => $oldField['field_type'],
                    'remark'           => $oldField['remark'],
                    'field_label'      => $rename,
                    'field_name'       => FieldConfigModel::getNextFieldName(),
                    'field_component'  => $oldField['field_component'],
                    'field_sort'       => $oldField['field_sort'],
                    'module_id'        => $oldField['module_id'],
                    'project_id'       => $newProjectId,
                    'category_id'      => $oldField['category_id'],
                    'allow_setting'    => $oldField['allow_setting'],
                    'is_edit'          => $oldField['is_edit'],
                    'template_default' => $oldField['template_default'],
                    'component_type'   => $oldField['component_type']
                ];
                $model->save($data);

                if (isset($diffData[$newProjectId])) {
                    $res['data'][$newProjectId]['field_config'][] = ['field_id' => $model->field_id, 'field_label' => $rename, 'project_id' => $newProjectId, 'url' => self::RENAME_URL];
                    $res['data'][$newProjectId]['project_name'] = $newName[$newProjectId] ?? '';
                }
            }

            DBTransaction::commit();
        } catch (\Throwable $e) {
            DBTransaction::rollback();
            throw $e;
        }

        $res['data'] = array_values($res['data']);
        return $res;
    }

    /**
     * 更新字段名称
     * @param int $fieldId
     * @param string $fieldLabel
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2025/4/14
     */
    public function rename(int $fieldId, string $fieldLabel)
    {
        $model = FieldConfigModel::findById($fieldId);
        if ( ! $model) {
            throw new NotFoundException();
        }

        $fieldLabelExist = FieldConfigModel::findFieldAndName($model->project_id, $fieldLabel);
        $projectInfo = ProjectInfoLogic::getProjectData($model->project_id);

        if ($fieldLabelExist && $fieldLabelExist->field_id != $fieldId) {
            $name = match ($projectInfo->is_template) {
                ProjectTemplateModel::IS_TEMPLATE_YES => ProjectTemplateLogic::getProductNameByProductId($projectInfo->project_template_id),
                ProjectTemplateModel::IS_TEMPLATE_NO => '【'.$projectInfo->project_name.'】',
                default => '',
            };
            throw new ParamsException($name .'-字段名称已存在');
        }

        // 校验名称是否与模板相同
        $this->validateProjectFieldData($projectInfo, $fieldLabel);

        $model->field_label = $fieldLabel;
        $model->save();
    }
}
