<?php
/**
 * Desc 统计查询验证器
 * User AI Assistant
 * Date 2024/12/20
 */

declare(strict_types=1);

namespace app\statistics\validate;

use think\Validate;

class StatisticsValidate extends Validate
{
    protected $rule = [
        'project_id' => 'require|integer|gt:0',
        'iteration_id' => 'integer|gt:0',
        'start_date' => 'date',
        'end_date' => 'date',
        'change_reason' => 'string',
        'type_code' => 'string',
        'node_stage' => 'string',
        'date' => 'date',
        'user_id' => 'integer|gt:0',
        'statistics' => 'integer|in:1,2',
        'submitter_uid' => 'require|integer|gt:0',
    ];

    protected $message = [
        'project_id.require' => '项目ID不能为空',
        'project_id.integer' => '项目ID必须为整数',
        'project_id.gt' => '项目ID必须大于0',
        'iteration_id.integer' => '迭代ID必须为整数',
        'iteration_id.gt' => '迭代ID必须大于0',
        'start_date.date' => '开始日期格式不正确',
        'end_date.date' => '结束日期格式不正确',
        'change_reason.string' => '变更原因必须为字符串',
        'type_code.string' => '类型代码必须为字符串',
        'node_stage.string' => '节点阶段必须为字符串',
        'date.date' => '日期格式不正确',
        'user_id.integer' => '用户ID必须为整数',
        'user_id.gt' => '用户ID必须大于0',
        'statistics.integer' => '统计方式必须为整数',
        'statistics.in' => '统计方式只能为1(今日)或2(昨日)',
        'submitter_uid.require' => '提交人ID不能为空',
        'submitter_uid.integer' => '提交人ID必须为整数',
        'submitter_uid.gt' => '提交人ID必须大于0',
    ];

    protected $scene = [
        'demand_change' => ['project_id', 'iteration_id', 'start_date', 'end_date', 'change_reason'],
        'new_demand' => ['project_id', 'iteration_id', 'start_date', 'end_date'],
        'demand_review' => ['project_id', 'type_code', 'start_date', 'end_date'],
        'development_delay' => ['project_id', 'iteration_id', 'node_stage', 'start_date', 'end_date'],
        'bug_statistics' => ['project_id', 'date', 'user_id', 'statistics'],
        'work_hours_rejection' => ['submitter_uid', 'project_id', 'start_date', 'end_date'],
        'iteration_level_statistics' => ['project_id', 'iteration_id'],
        'iteration_node_level_statistics' => ['project_id', 'iteration_id'],
        'test_delay' => ['project_id', 'iteration_id', 'node_stage', 'start_date', 'end_date'],
        'development_delay_enhanced' => ['project_id', 'iteration_id', 'node_stage', 'start_date', 'end_date'],
    ];
}
