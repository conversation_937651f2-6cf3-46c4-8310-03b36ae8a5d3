<?php
/**
 * Desc 统计查询逻辑层
 * User AI Assistant
 * Date 2024/12/20
 */

declare(strict_types=1);

namespace app\statistics\logic;

use think\facade\Db;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

class StatisticsLogic
{
    /**
     * 场景1: 获取需求变更记录
     * @param array $params
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getDemandChangeRecords(array $params): array
    {
        $sql = "SELECT p.project_name AS '所属项目',
                       wicr.cnt_id AS '需求ID',
                       wicr.cnt_title AS '需求标题',
                       wicr.handler_uid AS '需求负责人id',
                       wicr.handler_name AS '需求负责人',
                       i.iteration_name AS '迭代',
                       wicr.node_name AS '迭代节点',
                       ttl_stage.tag_name  AS '迭代节点阶段',
                       wicr.change_reason AS '变更原因',
                       wicr.submitter_name AS '提交人',
                       wicr.submit_at AS '提交时间'
                FROM t_work_items_change_records wicr
                LEFT JOIN t_project p ON wicr.project_id = p.project_id
                LEFT JOIN t_iteration i ON wicr.iteration_id = i.iteration_id
                LEFT JOIN t_tag_library ttl_stage ON wicr.node_stage = ttl_stage.tag_code
                WHERE 1=1";

        $bindings = [];

        // 必填参数
        if (!empty($params['project_id'])) {
            $sql .= " AND wicr.project_id = ?";
            $bindings[] = $params['project_id'];
        }

        // 可选参数
        if (!empty($params['iteration_id'])) {
            $sql .= " AND wicr.iteration_id = ?";
            $bindings[] = $params['iteration_id'];
        }

        if (!empty($params['change_reason'])) {
            $sql .= " AND wicr.change_reason LIKE ?";
            $bindings[] = "%{$params['change_reason']}%";
        }

        if (!empty($params['start_date']) && !empty($params['end_date'])) {
            $sql .= " AND wicr.submit_at BETWEEN ? AND ?";
            $bindings[] = $params['start_date'];
            $bindings[] = $params['end_date'];
        }

        $sql .= " ORDER BY wicr.submit_at DESC";

        $result = Db::query($sql, $bindings);

        // 转换为Excel格式：第一行是列名，后面是数据
        if (empty($result)) {
            return [
                ['所属项目', '需求ID', '需求标题', '需求负责人id', '需求负责人', '迭代', '迭代节点', '迭代节点阶段', '变更原因', '提交人', '提交时间']
            ];
        }

        // 提取列名（中文别名）
        $columns = array_keys($result[0]);

        // 提取数据行
        $rows = [];
        foreach ($result as $row) {
            $rows[] = array_values($row);
        }

        // 返回格式：[列名数组, 数据行1, 数据行2, ...]
        return array_merge([$columns], $rows);
    }

    /**
     * 场景2: 获取新增需求记录
     * @param array $params
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getNewDemandRecords(array $params): array
    {
        $sql = "SELECT idrr.project_id AS '所属项目',
                       idrr.cnt_id AS '需求ID',
                       idrr.cnt_title AS '需求标题',
                       idrr.operator_name AS '操作人',
                       idrr.iteration_name AS '迭代',
                       idrr.node_name AS '迭代节点',
                       ttl_stage.tag_name AS '迭代节点阶段',
                       idrr.operation_time AS '操作时间'
                FROM t_iteration_demand_relation_records idrr
                LEFT JOIN t_tag_library ttl_stage
                    ON idrr.node_stage = ttl_stage.tag_code AND ttl_stage.group_type = 1
                WHERE idrr.operation_type = 1";

        $bindings = [];

        // 必填参数
        if (!empty($params['project_id'])) {
            $sql .= " AND idrr.project_id = ?";
            $bindings[] = $params['project_id'];
        }

        // 可选参数
        if (!empty($params['iteration_id'])) {
            $sql .= " AND idrr.iteration_id = ?";
            $bindings[] = $params['iteration_id'];
        }

        if (!empty($params['start_date']) && !empty($params['end_date'])) {
            $sql .= " AND idrr.operation_time BETWEEN ? AND ?";
            $bindings[] = $params['start_date'];
            $bindings[] = $params['end_date'];
        }

        $sql .= " ORDER BY idrr.operation_time DESC";

        $result = Db::query($sql, $bindings);

        // 转换为Excel格式：第一行是列名，后面是数据
        if (empty($result)) {
            return [
                ['所属项目', '需求ID', '需求标题', '操作人', '迭代', '迭代节点', '迭代节点阶段', '操作时间']
            ];
        }

        // 提取列名和数据
        $columns = array_keys($result[0]);
        $rows = [];
        foreach ($result as $row) {
            $rows[] = array_values($row);
        }

        return array_merge([$columns], $rows);
    }

    /**
     * 场景3/7/10: 获取需求评审记录（场景3、7、10使用相同SQL）
     * @param array $params
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getDemandReviewRecords(array $params): array
    {
        $sql = "SELECT mcc.change_id,
                       p.project_name AS '所属项目',
                       mcc.task_id AS '任务ID',
                       mcc.task_title AS '任务标题',
                       GROUP_CONCAT(DISTINCT nr_user.user_name SEPARATOR ', ') AS '节点负责人',
                       GROUP_CONCAT(DISTINCT spk_user.user_name SEPARATOR ', ') AS '主讲人',
                       ttl.tag_name AS '会议类型名称',
                       ttl.tag_code AS '会议类型代码'
                FROM t_meeting_collection_change mcc
                JOIN t_meeting_collection_change_type mcct ON mcc.change_id = mcct.change_id
                JOIN t_tag_library ttl ON mcct.type_code = ttl.tag_code AND ttl.group_type = 2
                LEFT JOIN t_project p ON mcc.project_id = p.project_id
                LEFT JOIN t_meeting_collection_change_user mccu_nr ON mcc.change_id = mccu_nr.change_id AND mccu_nr.user_type = 1
                LEFT JOIN t_project_user nr_user ON mccu_nr.user_id = nr_user.user_id
                LEFT JOIN t_meeting_collection_change_user mccu_spk ON mcc.change_id = mccu_spk.change_id AND mccu_spk.user_type = 2
                LEFT JOIN t_project_user spk_user ON mccu_spk.user_id = spk_user.user_id
                WHERE mcc.is_delete = 0";

        $bindings = [];

        // 必填参数
        if (!empty($params['project_id'])) {
            $sql .= " AND mcc.project_id = ?";
            $bindings[] = $params['project_id'];
        }

        // 可选参数
        if (!empty($params['type_code'])) {
            $sql .= " AND mcct.type_code = ?";
            $bindings[] = $params['type_code'];
        }

        $sql .= " GROUP BY mcc.change_id, p.project_name, mcc.task_id, mcc.task_title, ttl.tag_name, ttl.tag_code, mcc.create_at";
        $sql .= " ORDER BY mcc.create_at DESC";

        $result = Db::query($sql, $bindings);

        // 转换为Excel格式：第一行是列名，后面是数据
        if (empty($result)) {
            return [
                ['change_id', '所属项目', '任务ID', '任务标题', '节点负责人', '主讲人', '会议类型名称', '会议类型代码']
            ];
        }

        // 提取列名和数据
        $columns = array_keys($result[0]);
        $rows = [];
        foreach ($result as $row) {
            $rows[] = array_values($row);
        }

        return array_merge([$columns], $rows);
    }

    /**
     * 场景4: 获取开发延期记录（带用户名解析）
     * @param array $params
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getDevelopmentDelayRecords(array $params): array
    {
        $sql = "WITH UserNamesCTE AS (
                    SELECT inner_ipn.iteration_process_node_id,
                           GROUP_CONCAT(pu.user_name SEPARATOR ', ') AS ResponsibleUserNames
                    FROM t_iteration_process_node inner_ipn
                    CROSS JOIN JSON_TABLE(
                        inner_ipn.node_data,
                        '$.node_manger.users[*]' COLUMNS (json_user_id INT PATH '$')
                    ) AS jt
                    JOIN t_project_user pu ON jt.json_user_id = pu.user_id AND pu.project_id = ?
                    GROUP BY inner_ipn.iteration_process_node_id
                )
                SELECT p.project_name AS '所属项目',
                       i.iteration_name AS '迭代',
                       ipn.node_name AS '迭代节点',
                       ttl_stage.tag_name AS '节点阶段',
                       r.estimate_start_time AS '预估开始时间',
                       r.estimate_end_time AS '预估结束时间',
                       r.actual_start_time AS '实际开始时间',
                       r.actual_end_time AS '实际结束时间',
                       unc.ResponsibleUserNames AS '节点负责人',
                       CASE WHEN r.is_delayed = 1 THEN '是' ELSE '否' END AS '是否延期'
                FROM t_iteration_process_node_end_record r
                JOIN t_iteration_process_node ipn ON r.iteration_process_node_id = ipn.iteration_process_node_id
                LEFT JOIN UserNamesCTE unc ON r.iteration_process_node_id = unc.iteration_process_node_id
                LEFT JOIN t_tag_library ttl_stage ON r.node_stage = ttl_stage.tag_code AND ttl_stage.group_type = 1
                LEFT JOIN t_project p ON r.project_id = p.project_id
                LEFT JOIN t_iteration i ON r.iteration_id = i.iteration_id
                WHERE r.project_id = ? AND r.is_delayed = 1";

        $bindings = [
            $params['project_id'], // UserNamesCTE 中的 project_id
            $params['project_id']  // 主查询中的 project_id
        ];

        // 可选参数
        if (!empty($params['iteration_id'])) {
            $sql .= " AND r.iteration_id = ?";
            $bindings[] = $params['iteration_id'];
        }

        if (!empty($params['node_stage'])) {
            $sql .= " AND ttl_stage.tag_code = ?";
            $bindings[] = $params['node_stage'];
        } else {
            // 默认筛选开发相关节点
            $sql .= " AND ttl_stage.tag_code = 'tag_49809162008138929929038'";
        }

        if (!empty($params['start_date']) && !empty($params['end_date'])) {
            $sql .= " AND r.actual_end_time BETWEEN ? AND ?";
            $bindings[] = $params['start_date'];
            $bindings[] = $params['end_date'];
        }

        $sql .= " ORDER BY r.actual_end_time DESC";

        $result = Db::query($sql, $bindings);

        // 转换为Excel格式：第一行是列名，后面是数据
        if (empty($result)) {
            return [
                ['所属项目', '迭代', '迭代节点', '节点阶段', '预估开始时间', '预估结束时间', '实际开始时间', '实际结束时间', '节点负责人', '是否延期']
            ];
        }

        // 提取列名和数据
        $columns = array_keys($result[0]);
        $rows = [];
        foreach ($result as $row) {
            $rows[] = array_values($row);
        }

        return array_merge([$columns], $rows);
    }

    /**
     * 场景5-字段1: 获取Bug统计记录（统计时间、处理人）
     * @param array $params
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getBugStatisticsRecords(array $params): array
    {
        $sql = "SELECT bs.id,
                       bs.project_id,
                       bs.iteration_id,
                       bs.date AS '统计日期',
                       bs.time AS '统计时间点',
                       u_handler.user_name AS '处理人姓名',
                       bs.user_id AS '处理人ID',
                       bs.count,
                       p.project_name AS '所属项目',
                       CASE bs.statistics
                           WHEN 1 THEN '今日'
                           WHEN 2 THEN '昨日'
                           ELSE CONCAT('未知统计方式 (', bs.statistics, ')')
                       END AS '统计方式',
                       bs.date AS '统计范围（日期）',
                       bs.time AS '统计截止时间',
                       bs.count AS '缺陷未清数量'
                FROM t_bug_statistics bs
                LEFT JOIN t_project p ON bs.project_id = p.project_id
                LEFT JOIN t_project_user u_handler ON bs.user_id = u_handler.user_id AND u_handler.project_id = bs.project_id
                WHERE bs.is_delete = 0 AND bs.count > 0";

        $bindings = [];

        // 必填参数
        if (!empty($params['project_id'])) {
            $sql .= " AND bs.project_id = ?";
            $bindings[] = $params['project_id'];
        }

        // 可选参数
        if (!empty($params['date'])) {
            $sql .= " AND bs.date = ?";
            $bindings[] = $params['date'];
        }

        if (!empty($params['user_id'])) {
            $sql .= " AND bs.user_id = ?";
            $bindings[] = $params['user_id'];
        }

        if (!empty($params['statistics'])) {
            $sql .= " AND bs.statistics = ?";
            $bindings[] = $params['statistics'];
        }

        $sql .= " ORDER BY bs.date DESC, bs.time DESC, u_handler.user_name";

        $result = Db::query($sql, $bindings);

        // 转换为Excel格式：第一行是列名，后面是数据
        if (empty($result)) {
            return [
                ['id', 'project_id', 'iteration_id', '统计日期', '统计时间点', '处理人姓名', '处理人ID', 'count', '所属项目', '统计方式', '统计范围（日期）', '统计截止时间', '缺陷未清数量']
            ];
        }

        // 提取列名和数据
        $columns = array_keys($result[0]);
        $rows = [];
        foreach ($result as $row) {
            $rows[] = array_values($row);
        }

        return array_merge([$columns], $rows);
    }

    /**
     * 场景5-字段2: 获取Bug统计记录（所属项目、统计方式、统计范围等）
     * @param array $params
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getBugStatisticsRecordsField2(array $params): array
    {
        // 这个方法与字段1相同，但注释说明不同的业务含义
        return $this->getBugStatisticsRecords($params);
    }

    /**
     * 场景6: 获取工时评估打回记录
     * @param array $params
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getWorkHoursRejectionRecords(array $params): array
    {
        $sql = "SELECT whrr.cnt_id AS '任务ID',
                       whrr.cnt_title AS '任务标题',
                       whrr.handler_name AS '处理人',
                       whrr.rejection_reason AS '打回原因',
                       whrr.submitter_name AS '提交人',
                       whrr.submit_at AS '提交时间'
                FROM t_work_hours_rejection_records whrr";

        $bindings = [];
        $whereConditions = [];

        // 必填参数
        if (!empty($params['submitter_uid'])) {
            $whereConditions[] = "whrr.submitter_uid = ?";
            $bindings[] = $params['submitter_uid'];
        }

        // 可选参数
        if (!empty($params['project_id'])) {
            // 如果需要通过项目ID筛选，需要JOIN work_items表
            $sql .= " LEFT JOIN t_work_items wi ON whrr.cnt_id = wi.cnt_id";
            $whereConditions[] = "JSON_UNQUOTE(wi.extends->'$.project_id') = ?";
            $bindings[] = $params['project_id'];
        }

        if (!empty($params['start_date']) && !empty($params['end_date'])) {
            $whereConditions[] = "whrr.create_at BETWEEN ? AND ?";
            $bindings[] = $params['start_date'];
            $bindings[] = $params['end_date'];
        }

        if (!empty($whereConditions)) {
            $sql .= " WHERE " . implode(' AND ', $whereConditions);
        }

        $sql .= " ORDER BY whrr.submit_at DESC";

        $result = Db::query($sql, $bindings);

        // 转换为Excel格式：第一行是列名，后面是数据
        if (empty($result)) {
            return [
                ['任务ID', '任务标题', '处理人', '打回原因', '提交人', '提交时间']
            ];
        }

        // 提取列名和数据
        $columns = array_keys($result[0]);
        $rows = [];
        foreach ($result as $row) {
            $rows[] = array_values($row);
        }

        return array_merge([$columns], $rows);
    }

    /**
     * 场景8-字段1: 获取迭代级别统计
     * @param array $params
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getScene8Field1Statistics(array $params): array
    {
        $sql = "WITH IterationChanges AS (
                    SELECT iteration_id,
                           project_id,
                           SUM(CASE WHEN change_reason LIKE '%需求变更%' THEN 1 ELSE 0 END) AS demand_change_count
                    FROM t_work_items_change_records
                    WHERE project_id = ?
                    GROUP BY iteration_id, project_id
                ),
                IterationNewDemandsFromRelation AS (
                    SELECT iteration_id,
                           project_id,
                           COUNT(DISTINCT cnt_id) as new_demand_relation_count
                    FROM t_iteration_demand_relation_records
                    WHERE operation_type = 1 AND project_id = ?
                    GROUP BY iteration_id, project_id
                ),
                IterationOverallDelayStatus AS (
                    SELECT iteration_id,
                           project_id,
                           MAX(is_delayed) AS is_overall_delayed
                    FROM t_iteration_process_node_end_record
                    WHERE project_id = ?
                    GROUP BY iteration_id, project_id
                )
                SELECT p.project_name AS '所属项目',
                       i.iteration_name AS '迭代',
                       COALESCE(ic.demand_change_count, 0) AS '需求变更次数',
                       COALESCE(indr.new_demand_relation_count, 0) AS '需求新增次数',
                       CONCAT(i.estimate_start_time, ' 至 ', i.estimate_end_time) AS '迭代预估周期',
                       CONCAT(i.start_time, ' 至 ', i.end_time) AS '迭代实际周期',
                       CASE
                           WHEN iods.is_overall_delayed = 1 THEN '是'
                           WHEN iods.is_overall_delayed = 0 THEN '否'
                           ELSE
                               (CASE
                                    WHEN i.end_time > i.estimate_end_time THEN '是'
                                    ELSE '否'
                                END)
                       END AS '是否延期'
                FROM t_iteration i
                LEFT JOIN t_project p ON i.project_id = p.project_id
                LEFT JOIN IterationChanges ic ON i.iteration_id = ic.iteration_id AND i.project_id = ic.project_id
                LEFT JOIN IterationNewDemandsFromRelation indr ON i.iteration_id = indr.iteration_id AND i.project_id = indr.project_id
                LEFT JOIN IterationOverallDelayStatus iods ON i.iteration_id = iods.iteration_id AND i.project_id = iods.project_id
                WHERE i.project_id = ?";

        $bindings = [
            $params['project_id'], // IterationChanges CTE
            $params['project_id'], // IterationNewDemandsFromRelation CTE
            $params['project_id'], // IterationOverallDelayStatus CTE
            $params['project_id']  // Main query
        ];

        // 可选参数
        if (!empty($params['iteration_id'])) {
            $sql .= " AND i.iteration_id = ?";
            $bindings[] = $params['iteration_id'];
        }

        $sql .= " ORDER BY i.iteration_name";

        $result = Db::query($sql, $bindings);

        // 转换为Excel格式：第一行是列名，后面是数据
        if (empty($result)) {
            return [
                ['所属项目', '迭代', '需求变更次数', '需求新增次数', '迭代预估周期', '迭代实际周期', '是否延期']
            ];
        }

        // 提取列名和数据
        $columns = array_keys($result[0]);
        $rows = [];
        foreach ($result as $row) {
            $rows[] = array_values($row);
        }

        return array_merge([$columns], $rows);
    }

    /**
     * 场景8-字段2: 获取迭代节点级别统计
     * @param array $params
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getScene8Field2Statistics(array $params): array
    {
        $sql = "WITH NodeDemandChanges AS (
                    SELECT node_id,
                           project_id,
                           iteration_id,
                           COUNT(change_id) AS node_demand_change_count
                    FROM t_work_items_change_records
                    WHERE (change_reason LIKE '%需求变更%' OR change_reason LIKE '%some_other_demand_change_indicator%')
                    GROUP BY node_id, project_id, iteration_id
                ),
                NodeNewDemands AS (
                    SELECT node_id,
                           project_id,
                           iteration_id,
                           COUNT(record_id) AS node_new_demand_count
                    FROM t_iteration_demand_relation_records
                    WHERE operation_type = 1
                    GROUP BY node_id, project_id, iteration_id
                )
                SELECT ipn.process_node_id AS '迭代id',
                       ipn.node_name AS '迭代节点',
                       ttl_stage.tag_name AS '阶段',
                       COALESCE(ndc.node_demand_change_count, 0) AS '需求变更次数',
                       COALESCE(nnd.node_new_demand_count, 0) AS '需求新增次数',
                       i.iteration_name AS '所属迭代',
                       p.project_name AS '所属项目'
                FROM t_iteration_process_node ipn
                LEFT JOIN t_iteration i ON ipn.iteration_id = i.iteration_id
                LEFT JOIN t_project p ON i.project_id = p.project_id
                LEFT JOIN t_tag_library ttl_stage ON JSON_UNQUOTE(ipn.node_data->'$.node_setting.node_stage') = ttl_stage.tag_code
                    AND ttl_stage.group_type = 1
                LEFT JOIN NodeDemandChanges ndc ON ipn.iteration_process_node_id = ndc.node_id
                    AND ipn.iteration_id = ndc.iteration_id AND i.project_id = ndc.project_id
                LEFT JOIN NodeNewDemands nnd ON ipn.iteration_process_node_id = nnd.node_id
                    AND ipn.iteration_id = nnd.iteration_id AND i.project_id = nnd.project_id
                WHERE i.project_id = ? AND ipn.is_delete = 0";

        $bindings = [$params['project_id']];

        // 可选参数
        if (!empty($params['iteration_id'])) {
            $sql .= " AND i.iteration_id = ?";
            $bindings[] = $params['iteration_id'];
        }

        $sql .= " ORDER BY i.iteration_name, ipn.create_at, ipn.iteration_process_node_id";

        $result = Db::query($sql, $bindings);

        // 转换为Excel格式：第一行是列名，后面是数据
        if (empty($result)) {
            return [
                ['迭代id', '迭代节点', '阶段', '需求变更次数', '需求新增次数', '所属迭代', '所属项目']
            ];
        }

        // 提取列名和数据
        $columns = array_keys($result[0]);
        $rows = [];
        foreach ($result as $row) {
            $rows[] = array_values($row);
        }

        return array_merge([$columns], $rows);
    }

    /**
     * 场景9: 获取测试延期记录（带用户名解析）
     * @param array $params
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getTestDelayRecords(array $params): array
    {
        $sql = "WITH UserNamesCTE AS (
                    SELECT inner_ipn.iteration_process_node_id,
                           GROUP_CONCAT(pu.user_name SEPARATOR ', ') AS ResponsibleUserNames
                    FROM t_iteration_process_node inner_ipn
                    CROSS JOIN JSON_TABLE(
                        inner_ipn.node_data,
                        '$.node_manger.users[*]' COLUMNS (json_user_id INT PATH '$')
                    ) AS jt
                    JOIN t_project_user pu ON jt.json_user_id = pu.user_id AND pu.project_id = ?
                    GROUP BY inner_ipn.iteration_process_node_id
                )
                SELECT p.project_name AS '所属项目',
                       i.iteration_name AS '迭代',
                       ipn.node_name AS '迭代节点',
                       ttl_stage.tag_name AS '节点阶段',
                       r.estimate_start_time AS '预估开始时间',
                       r.estimate_end_time AS '预估结束时间',
                       r.actual_start_time AS '实际开始时间',
                       r.actual_end_time AS '实际结束时间',
                       unc.ResponsibleUserNames AS '节点负责人',
                       r.group_leader_name AS '组长',
                       CASE WHEN r.is_delayed = 1 THEN '是' ELSE '否' END AS '是否延期'
                FROM t_iteration_process_node_end_record r
                JOIN t_iteration_process_node ipn ON r.iteration_process_node_id = ipn.iteration_process_node_id
                LEFT JOIN UserNamesCTE unc ON r.iteration_process_node_id = unc.iteration_process_node_id
                LEFT JOIN t_tag_library ttl_stage ON r.node_stage = ttl_stage.tag_code AND ttl_stage.group_type = 1
                LEFT JOIN t_project p ON r.project_id = p.project_id
                LEFT JOIN t_iteration i ON r.iteration_id = i.iteration_id
                WHERE r.project_id = ? AND r.is_delete = 0 AND r.is_delayed = 1";

        $bindings = [
            $params['project_id'], // UserNamesCTE 中的 project_id
            $params['project_id']  // 主查询中的 project_id
        ];

        // 可选参数
        if (!empty($params['iteration_id'])) {
            $sql .= " AND r.iteration_id = ?";
            $bindings[] = $params['iteration_id'];
        }

        if (!empty($params['node_stage'])) {
            $sql .= " AND ttl_stage.tag_code = ?";
            $bindings[] = $params['node_stage'];
        } else {
            // 默认筛选测试相关节点
            $sql .= " AND ttl_stage.tag_code = 'tag_49809162267640520716923'";
        }

        if (!empty($params['start_date']) && !empty($params['end_date'])) {
            $sql .= " AND r.actual_end_time BETWEEN ? AND ?";
            $bindings[] = $params['start_date'];
            $bindings[] = $params['end_date'];
        }

        $sql .= " ORDER BY r.actual_end_time DESC";

        $result = Db::query($sql, $bindings);

        // 转换为Excel格式：第一行是列名，后面是数据
        if (empty($result)) {
            return [
                ['所属项目', '迭代', '迭代节点', '节点阶段', '预估开始时间', '预估结束时间', '实际开始时间', '实际结束时间', '节点负责人', '组长', '是否延期']
            ];
        }

        // 提取列名和数据
        $columns = array_keys($result[0]);
        $rows = [];
        foreach ($result as $row) {
            $rows[] = array_values($row);
        }

        return array_merge([$columns], $rows);
    }
}
