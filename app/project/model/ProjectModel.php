<?php
/**
 * Desc 项目 - 模型
 * User Long
 * Date 2024/07/19*/

declare (strict_types=1);

namespace app\project\model;

use app\infrastructure\model\EnumModel;
use app\product\model\ProductModel;
use basic\BaseModel;
use think\Collection;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\model\relation\HasMany;
use think\model\relation\HasOne;
use traits\CreateAndUpdateModelTrait;

/**
 * This is the model class for table "project".
 * @property string $project_id id
 * @property string $create_by 创建人
 * @property string $create_by_name 创建人名称
 * @property string $create_at 创建时间
 * @property string $is_delete 是否删除;1-是 0-否
 * @property string $update_by 更新人
 * @property string $update_by_name 更新人名称
 * @property string $update_at 更新时间
 * @property string $project_name 项目名称
 * @property string $product_id 产品Id
 * @property string $project_no 项目编号
 * @property string $project_user_count 项目成员数量
 * @property string $project_status 项目状态1进行中2关闭
 * @property string $project_remark 项目描述
 * @property string $project_template_id 项目模板id
 * @property string $project_icon 项目icon
 * @property string $is_template 是否是模板;1-是 0-否
 */
class ProjectModel extends BaseModel
{
    use CreateAndUpdateModelTrait;

    protected $pk = 'project_id';
    protected $name = 'project';

    const HIDDEN_FIELD = [
        'update_by', 'update_by_name', 'update_at', 'is_delete', 'project_no',
        'project_remark', 'project_template_id', 'is_template'
    ];

    // 是否是模板;1-是 0-否
    const IS_TEMPLATE_YES = 1;
    const IS_TEMPLATE_NOT = 0;

    /**
     * 未删除状态
     * @return static
     * User Long
     * Date 2024/7/24
     */
    public static function status()
    {
        return static::where(['is_delete' => self::DELETE_NOT, 'is_template' => self::IS_TEMPLATE_NOT]);
    }

    /**
     * 根据id集查询数据
     * @param array $projectIds
     * @return ProjectModel[]|array|Collection
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/10/10
     */
    public static function selectByIds(array $projectIds)
    {
        return static::status()->whereIn('project_id', $projectIds)->select();
    }


    /**
     * 根据 id 查询数据
     * @param int $projectId
     * @return mixed
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/10/10
     */
    public static function findById(int $projectId): mixed
    {
        return static::status()->where(['project_id' => $projectId])->find();
    }


    /**
     * 详情页处理
     * @return ProjectModel
     * User Long
     * Date 2024/10/10
     */
    public function toDetail()
    {
        return $this->hidden(['is_delete', 'is_template', 'project_no']);
    }

    /**
     * 预加载 - 产品表
     * @return HasOne
     * User Long
     * Date 2024/9/2
     */
    public function product(): HasOne
    {
        return $this->hasOne(ProductModel::class, 'product_id', 'product_id');
    }

    /**
     * 预加载 - 收藏表
     * @return HasOne
     * User Long
     * Date 2024/9/2
     */
    public function favorite(): HasOne
    {
        return $this->hasOne(ProjectFavoriteModel::class, 'project_id', 'project_id');
    }

    /**
     * 预加载 - 成员表
     * @return HasMany
     * User Long
     * Date 2024/10/11
     */
    public function projectManager(): HasMany
    {
        return $this->HasMany(ProjectUserModel::class, 'project_id', 'project_id');
    }

    /**
     * 获取器 - 获取产品名称
     * @param $value
     * @param $data
     * @return string
     * User Long
     * Date 2024/8/10
     */
    public function getProductNameAttr($value, $data): string
    {
        return $value ?? '';
    }

    /**
     * 获取系统中所有项目ID
     * @return array 项目ID数组
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/07/24
     */
    public static function getAllProjectIds(): array
    {
        return static::where('is_delete', self::DELETE_NOT)
            ->where('is_template', self::IS_TEMPLATE_NOT)
            ->column('project_id');
    }
}
