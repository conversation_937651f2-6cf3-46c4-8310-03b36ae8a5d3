<?php
/**
 * Desc 迭代目录 - 逻辑处理
 * User Long
 * Date 2024/11/8
 */

namespace app\project\logic;

use app\infrastructure\model\EnumModel;
use app\iterate\logic\FlowProcessLogic;
use app\iterate\logic\FlowStatusEnumLogic;
use app\iterate\logic\FlowStatusTextLogic;
use app\iterate\model\FlowStatusTextModel;
use app\project\model\IterationModel;
use app\project\model\IterationProcessNodeAuditModel;
use app\project\model\IterationProcessNodeModel;
use app\project\model\ProjectUserModel;
use app\project\validate\IterationCatalogValidate;
use app\project\validate\IterationProcessNodeValidate;
use app\work_items\logic\TestPlanLogic;
use app\work_items\logic\WorkItemsLogic;
use app\work_items\model\WorkItemsModel;
use basic\BaseLogic;
use basic\BaseModel;
use exception\BusinessException;
use exception\NotFoundException;
use exception\ParamsException;
use think\Collection;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\facade\Db;
use utils\Ctx;
use utils\DBTransaction;

class IterationCatalogLogic extends BaseLogic
{
    /**
     * 迭代icon
     * @return array
     * User Long
     * Date 2024/11/11
     */
    public function getIconPath(): array
    {
        return getSystemEnumLibrary(EnumModel::ITERATION_ICON);
    }

    /**
     * 新增
     * @param  array  $params
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws \Throwable
     * User Long
     * Date 2024/11/11
     */
    public function create(array $params): void
    {
        // 验证器校验
        validate(IterationCatalogValidate::class)->scene('create')->check($params);
        $params = unsetEmptyArray($params);

        $categorySettingsInfo = ProjectCategorySettingsLogic::findDataByCategorySettingsId($params['project_category_settings_id']);

        $params['flow_process_id'] = $categorySettingsInfo['categorySettingsData']->flow_process_id ?? 0;
        $params['flow_status_id'] = $categorySettingsInfo['categorySettingsData']->flow_status_id ?? 0;
        $params['status_text_id'] = $categorySettingsInfo['statusEnumData']->status_text_id ?? 0;
        $params['status_enum_id'] = $categorySettingsInfo['statusEnumData']->status_enum_id ?? 0;

        // 检查名称是否已存在
        $iterationNameExist = IterationModel::findIterationAndName((int)$params['project_id'], $params['iteration_name']);
        if ($iterationNameExist) {
            throw new ParamsException('迭代名称已存在');
        }

        try {
            DBTransaction::begin();

            $iterationModel = new IterationModel();
            $params['extends']['flow_status_id'] = $params['flow_status_id'];
            $params['extends']['status_text_id'] = $params['status_text_id'];
            $params['extends']['status_enum_id'] = $params['status_enum_id'];
            $params['extends'] = $this->updateExtendsCreateUser($params['extends']);
            $params['extends'] = $this->updateExtendsUpdateUser($params['extends']);

            $iterationModel->save($params);

            // 更新迭代ID
            $params['extends']['iteration_id'] = $iterationModel->iteration_id;
            $iterationModel->extends = $params['extends'];
            $iterationModel->save();

            // 复制工作流程模板数据
            FlowProcessLogic::copyProcessTemplate(
                (int)$params['project_id'],
                (int)$iterationModel->iteration_id,
                (int)$categorySettingsInfo['categorySettingsData']->flow_process_id ?? 0,
                (int)$categorySettingsInfo['categorySettingsData']->flow_status_id ?? 0,
                $params['extends']
            );

            DBTransaction::commit();
        } catch (\Throwable $e) {
            DBTransaction::rollback();
            throw $e;
        }
    }

    /**
     * 根据id集删除分类
     * @param  array  $iterationIds
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/11/11
     */
    public function delete(array $iterationIds): void
    {
        $models = IterationModel::selectByIds($iterationIds);

        try {
            DBTransaction::begin();

            foreach ($models as $model) {
                // 查询迭代相关工作项ID
                $cntIds = WorkItemsLogic::getItemByIterationId((int)$model->iteration_id);

                if ($cntIds) {
                    // 批量更新工作项迭代ID
                    WorkItemsLogic::setItemsIterationId($cntIds);
                }

                $model->save(['is_delete' => BaseModel::DELETE_YES]);
            }

            DBTransaction::commit();
        } catch (\Throwable $e) {
            DBTransaction::rollback();
            throw $e;
        }

    }

    /**
     * 根据id更新数据
     * @param  int    $iterationId
     * @param  array  $params
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/11/11
     */
    public function update(int $iterationId, array $params): void
    {
        // 验证器校验
        validate(IterationCatalogValidate::class)->scene('update')->check($params);
        $params = unsetEmptyArray($params);

        $iterationModel = IterationModel::findById($iterationId);
        if ( ! $iterationModel) {
            throw new NotFoundException();
        }

        // 检查名称是否已存在
        $iterationNameExist = IterationModel::findIterationAndName((int)$iterationModel->project_id, $params['iteration_name']);
        if ($iterationNameExist && $iterationNameExist->iteration_id != $iterationId) {
            throw new ParamsException('迭代名称已存在');
        }

        if ( ! empty($params['estimate_start_time']) && ! empty($params['estimate_end_time'])) {
            if ($params['estimate_start_time'] >= $params['estimate_end_time']) {
                throw new ParamsException('结束时间必须大于开始时间');
            }
        }

        $params['extends']['flow_status_id'] = $iterationModel->flow_status_id;
        $params['extends']['status_text_id'] = $iterationModel->status_text_id;
        $params['extends']['status_enum_id'] = $iterationModel->status_enum_id;
        $params['extends'] = $this->updateExtendsUpdateUser($params['extends']);
        $params['extends']['iteration_id'] = $iterationModel->iteration_id;

        $iterationModel->save($params);
    }

    /**
     * 更新迭代时间
     * @param  int    $iterationId
     * @param  array  $params
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/11/14
     */
    public function updateIterativeTime(int $iterationId, array $params): void
    {
        // 验证器校验
        validate(IterationCatalogValidate::class)->scene('updateIterativeTime')->check($params);

        $iterationModel = IterationModel::findById($iterationId);
        if ( ! $iterationModel) {
            throw new NotFoundException();
        }

        $params['extends'] = $this->updateExtendsUpdateUser($iterationModel->extends);
        $params['extends']['estimate_iteration_cycle'] = [$params['estimate_start_time'], $params['estimate_end_time']];

        // 设置默认时间
        if (empty($params['estimate_start_time'])) {
            $params['estimate_start_time'] = BaseModel::DEFAULT_TIME;
        }
        if (empty($params['estimate_end_time'])) {
            $params['estimate_end_time'] = BaseModel::DEFAULT_TIME;
        }

        $iterationModel->save($params);
    }

    /**
     * 详情
     * @param  int  $iterationId
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/11/11
     */
    public function detail(int $iterationId): array
    {
        $model = IterationModel::findById($iterationId);
        if ( ! $model) {
            throw new NotFoundException();
        }

        $res = $model->toDetail()->toArray();

        $statusEnumData = FlowStatusEnumLogic::findDemandByEnumId($res['status_enum_id']);
        $res['status_enum_info'] = [
            'label'  => $statusEnumData->name ?? '',
            'key'    => $statusEnumData->status_enum_id ?? 0,
            'colour' => $statusEnumData->colour ?? '',
        ];

        $categorySettingsData = ProjectCategorySettingsLogic::findCategorySettingsDataByCategorySettingsId($res['project_category_settings_id'], true);
        $res['project_category_settings_info'] = [
            'category_name'    => $categorySettingsData->category_name ?? '',
            'category_en_name' => $categorySettingsData->category_en_name ?? '',
            'icon'             => $categorySettingsData->icon ?? '',
            'template_id'      => $categorySettingsData->template_id ?? 0,
        ];


//        if ($res['extends']['iteration_leader'] ?? false) {
//            $userList = ProjectUserModel::findListNotDelById($res['extends']['iteration_leader'])->column('user_name', 'user_id');
//            $res['extends']['iteration_leader_name'] = array_map(function ($item) use ($userList) {
//                return ['label' => $userList[$item], 'value' => $item];
//            }, $res['extends']['iteration_leader']);
//        }

        return $res;
    }

    /**
     * 分页
     * @param  int|array  $projectId     项目ID
     * @param  array      $searchParams  // 搜索条件
     *                                   // "search_params": [
     *                                   //     // {
     *                                   //     //     "field_name": "iteration_name",
     *                                   //     //     "type": "text",
     *                                   //     //     "value": "5",
     *                                   //     //     "operate_type": ""
     *                                   //     // },
     *                                   //     {
     *                                   //         "field_name": "iteration_name",
     *                                   //         "type": "term",
     *                                   //         "value": "迭代4",
     *                                   //         "operate_type": "equal"
     *                                   //     },
     *                                   //     // {
     *                                   //     //     "field_name": "iteration_client_list",
     *                                   //         // "type": "selector",
     *                                   //     //     "value": [117, 302],
     *                                   //     //     "operate_type": ""
     *                                   //     // },
     *                                   //       {
     *                                   //         "field_name": "estimate_iteration_cycle",
     *                                   //         "type": "date",
     *                                   //         "value": ["2024-11-15", "2024-11-15"],
     *                                   //         "operate_type": ""
     *                                   //     }
     *
     * // ]
     * @param  bool       $isOff         是否关闭，null 所有，true 查询关闭，，false 查询开启
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     *                                   User Long
     *                                   Date 2024/11/14
     */
    public function listQuery(int|array $projectId, array $searchParams = [], bool $isOff = null)
    {
        $where = [];

        $model = IterationModel::status();

        // 兼容多项目查询
        if (is_array($projectId)) {
            $model = $model->whereIn('project_id', $projectId);
        } else {
            $model = $model->where('project_id', $projectId);
        }

        // 处理status_enum_id参数
        $statusEnumIdParam = null;
        foreach ($searchParams as $key => $search) {
            if ($search['field_name'] === 'status_enum_id' && $search['type'] === 'selector') {
                $statusEnumIdParam = $search['value'];
                // 从搜索参数中移除，后面单独处理
                unset($searchParams[$key]);
                break;
            }
        }

        // json 搜索
        if ($searchParams) {
            $model = $model->json(['extends']);

            foreach ($searchParams as $search) {
                switch ($search['type']) {
                case 'text':
                    $model = $model->where('extends->'.$search['field_name'], 'like', '%'.$search['value'].'%');
                    break;
                case 'term':
                    $op = match ($search['operate_type']) {
//                            'equal' => '=',
                        'not_equal' => '<>',
                        'between' => 'in',
                        default => '=',
                    };
                    $model = $model->where('extends->'.$search['field_name'], $op, $search['value']);
                    break;
                case 'selector':
                    $conditions = [];
                    foreach ($search['value'] as $value) {
                        $conditions[] = "JSON_CONTAINS(extends->'$.{$search['field_name']}', CAST('$value' AS JSON), '$')";
                    }
                    if (count($conditions) > 1) {
                        $conditions = implode(' OR ', $conditions);
                    } else {
                        $conditions = implode('', $conditions);
                    }

                    $model = $model->whereRaw("($conditions)");
                    break;
                case 'date':
                    if ( ! isset($search['value'][1])) {
                        $end_time = date('Y-m-d', strtotime($search['value'][0])).' 23:59:59';
                        // 数据库存的格式可能为 Y-m-d 使用当天 Y-m-d 00:00:00 无法查询数据
                        $start_time = date('Y-m-d 23:59:59', strtotime('-1 day', strtotime($end_time)));

                        // 构建查询
                        $query = "((JSON_EXTRACT(extends, '$.{$search['field_name']}[0]') > ? AND JSON_EXTRACT(extends, '$.{$search['field_name']}[0]') <= ?)";
                    } else {
                        $start_time = date('Y-m-d 23:59:59', strtotime('-1 day', strtotime($search['value'][0])));
                        $end_time = date('Y-m-d', strtotime($search['value'][1])).' 23:59:59';

                        // 构建查询
                        $query = "((JSON_EXTRACT(extends, '$.{$search['field_name']}[0]') > ? AND JSON_EXTRACT(extends, '$.{$search['field_name']}[1]') <= ?)";
                    }

                    $query .= " OR (JSON_EXTRACT(extends, '$.{$search['field_name']}') > ? AND JSON_EXTRACT(extends, '$.{$search['field_name']}') <= ?))";
                    $params = [$start_time, $end_time, $start_time, $end_time];

                    $model = $model->whereRaw($query, $params);
                    break;
                default:
                    break;
                }
            }
        }

        // 处理status_enum_id参数
        if ($statusEnumIdParam !== null) {
            // 如果数组为空或者包含null，则不进行过滤
            if (count($statusEnumIdParam) === 0 || in_array(null, $statusEnumIdParam)) {
                // 不进行过滤
            } // 如果包含-1，表示要查询结束状态
            elseif (in_array(-1, $statusEnumIdParam) && ! in_array(-2, $statusEnumIdParam)) {
                // -1代表结束状态
                // 获取所有结束状态的status_enum_id
                $statusTextIds = FlowStatusTextLogic::getFlowTextIdByStatusTypeOff();
                if ($statusTextIds) {
                    $model = $model->whereIn('status_text_id', $statusTextIds);
                }
            } // 如果包含-2，表示要查询未结束状态
            elseif (in_array(-2, $statusEnumIdParam) && ! in_array(-1, $statusEnumIdParam)) {
                // -2代表未结束状态
                // 获取所有结束状态的status_enum_id
                $statusTextIds = FlowStatusTextLogic::getFlowTextIdByStatusTypeOff();
                if ($statusTextIds) {
                    $model = $model->whereNotIn('status_text_id', $statusTextIds);
                }
            } // 如果同时包含-1和-2，表示要查询所有状态，不需要过滤
            elseif (in_array(-1, $statusEnumIdParam) && in_array(-2, $statusEnumIdParam)) {
                // 不进行过滤，直接返回所有数据
            } // 其他情况，按照指定的status_enum_id进行过滤
            else {
                // 过滤掉特殊值
                $normalStatusEnumIds = array_filter($statusEnumIdParam, function ($value) {
                    return $value !== -1 && $value !== -2 && $value !== null;
                });

                if ( ! empty($normalStatusEnumIds)) {
                    $model = $model->whereIn('status_enum_id', $normalStatusEnumIds);
                }
            }
        }

        // 状态为关闭的迭代，需要根据状态类型判断是否显示
        if (is_bool($isOff)) {
            $statusTextIds = FlowStatusTextLogic::getFlowTextIdByStatusTypeOff();

            if ($statusTextIds) {
                // 重新实例化查询迭代，避免in太长影响主查询
                $isOffModel = IterationModel::status();
                if ($isOff) {
                    $isOffModel = $isOffModel->whereIn('status_text_id', $statusTextIds);
                } else {
                    $isOffModel = $isOffModel->whereNotIn('status_text_id', $statusTextIds);
                }
                $iterationIds = $isOffModel->where(['project_id' => $projectId])->column('iteration_id');

                if ($iterationIds) {
                    $model = $model->whereIn('iteration_id', $iterationIds);
                }
            }
        }
//dump($isOffModel->getLastSql());
        $res = $model
            ->with([
                'statusEnum' => function ($sql) {
                    $sql->bind(['status_enum_name' => 'name', 'colour']);
                },
                'statusText' => function ($sql) {
                    $sql->bind(['status_type']);
                }
            ])
            ->where($where)
            ->hidden(IterationModel::HIDDEN_FIELD)
            ->order('iteration_id DESC')
            ->select()
            ->toArray();
//dd( $model->getLastSql());

        foreach ($res as &$item) {
            $item['extends']['iteration_progress'] = (new WorkItemsLogic())->completeProgress($item['iteration_id']) ?? 0; // 迭代进度

            $testPlan = TestPlanLogic::getInstance()->getAvgCompletenessByIterationId($item['iteration_id']);
            $item['extends']['test_plan_completion_progress'] = $testPlan['test_plan']['avg_execution_progress'] ?? 0; // 测试计划完成进度
            $item['extends']['self_test_plan_completion_progress'] = $testPlan['dev_self_test_plan']['avg_execution_progress'] ?? 0; // 开发自测计划完成进度
            $item['extends']['auto_completion_progress'] = $testPlan['automate']['avg_execution_progress'] ?? 0; // 自动化完成进度

            // 获取当前迭代工作项数据
            $workItemsData = WorkItemsLogic::findIterationDataByIterationId($item['iteration_id']);

            // 需求、缺陷、任务数量查询
            $demandWorkItemsData = $workItemsData->where('cnt_type', WorkItemsModel::CNT_TYPE_DEMAND);
            $item['extends']['demand_number'] = $demandWorkItemsData->count(); // 需求数
            $item['extends']['demand_incomplete_number'] = $demandWorkItemsData->where('isEnd', false)->count(); // 需求未完成数量
            $item['extends']['demand_complete_number'] = $demandWorkItemsData->where('isEnd', true)->count(); // 需求已完成数量

            $defectWorkItemsData = $workItemsData->where('cnt_type', WorkItemsModel::CNT_TYPE_FLAW);
            $item['extends']['defect_number'] = $defectWorkItemsData->count(); // 缺陷数
            $item['extends']['defect_incomplete_number'] = $defectWorkItemsData->where('isEnd', false)->count(); // 缺陷未完成数量
            $item['extends']['defect_complete_number'] = $defectWorkItemsData->where('isEnd', true)->count(); // 缺陷已完成数量

            $taskWorkItemsData = $workItemsData->where('cnt_type', WorkItemsModel::CNT_TYPE_TASK);
            $item['extends']['task_number'] = $taskWorkItemsData->count(); // 任务数
            $item['extends']['task_incomplete_number'] = $taskWorkItemsData->where('isEnd', false)->count(); // 任务未完成数量
            $item['extends']['task_complete_number'] = $taskWorkItemsData->where('isEnd', true)->count(); // 任务已完成数量
        }
        unset($item);

        return $res;
    }

    /**
     * 迭代下拉框
     * @param  int|array  $projectId
     * @param  int        $scenario
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/11/14
     */
    public function selectorIteration(int|array $projectId, int $scenario)
    {
        $res = [];

        if ($scenario) {
            $data = $this->listQuery($projectId, [], false);
        } else {
            $data = $this->listQuery($projectId);
        }


        if ( ! $data) {
            return $res;
        }

        foreach ($data as $item) {
            $res[] = ['label' => $item['iteration_name'], 'key' => $item['iteration_id']];
        }

        return $res;
    }

    /**
     * 获取迭代流程节点下拉框
     * @param  int  $iterationId
     * @return array
     * User Long
     * Date 2024/12/26
     */
    public function selectorProcessNode(int $iterationId)
    {
        return IterationProcessNodeLogic::selectorProcessNode($iterationId);
    }

    /**
     * 获取迭代流程节点
     * @param  int  $iterationId
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/11/12
     */
    public function getIterationProcessNode(int $iterationId)
    {
        return IterationProcessNodeLogic::getProcessNodeByIterationId($iterationId);
    }

    /**
     * 获取迭代流程节点详情
     * @param  int  $iterationProcessNodeId
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/11/13
     */
    public function getIterationProcessNodeDetail(int $iterationProcessNodeId)
    {
        return IterationProcessNodeLogic::detail($iterationProcessNodeId);
    }

    /**
     * 获取迭代默认用户角色
     * @param  int    $iterationProcessNodeId  节点 id
     * @param  array  $roles                   角色合集
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     *                                         User Long
     *                                         Date 2025/2/17
     */
    private function updateDefaultUserData(int $iterationProcessNodeId, array $roles)
    {
        $users = [];

        if ( ! $roles) {
            return $users;
        }

        $iterationInfoModel = IterationModel::findById((int)IterationProcessNodeLogic::getIterationIdByNodeId($iterationProcessNodeId));
        if ( ! $iterationInfoModel) {
            throw new NotFoundException('保存失败，迭代不存在，请刷新后重试！');
        }

        $iterationInfoExtends = $iterationInfoModel->extends;

        return $this->getDefaultExtendsData($iterationInfoExtends, $roles);
    }

    /**
     * 获取组装后的处理人数据
     * @param  array  $iterationExtends
     * @param  array  $roles
     * @return array
     * User Long
     * Date 2025/2/17
     */
    public function getDefaultExtendsData(array $iterationExtends, array $roles)
    {
        $users = [];

        if ( ! $roles) {
            return $users;
        }

        foreach ($roles as $role) {
            switch ($role) {
            case EnumModel::SOFTWARE_TEST: // 软件测试
                isset($iterationExtends['software_test']) && $users = array_merge($users, $iterationExtends['software_test']);
                break;
            case EnumModel::BACKEND_DEVELOPMENT: // 后端开发
                isset($iterationExtends['backend_development']) && $users = array_merge($users, $iterationExtends['backend_development']);
                break;
            case EnumModel::WEB_DEVELOP: // 前端开发
                isset($iterationExtends['web_develop']) && $users = array_merge($users, $iterationExtends['web_develop']);
                break;
            case EnumModel::UI_DESIGNER: // UI设计师
                isset($iterationExtends['ui_designer']) && $users = array_merge($users, $iterationExtends['ui_designer']);
                break;
            case EnumModel::PRODUCT_MANAGER: // 产品经理
                isset($iterationExtends['product_manager']) && $users = array_merge($users, $iterationExtends['product_manager']);
                break;
            case EnumModel::PROJECT_MANAGER: // 项目经理
                isset($iterationExtends['project_manager']) && $users = array_merge($users, $iterationExtends['project_manager']);
                break;
            case EnumModel::ITERATION_LEADER: // 迭代leader
                isset($iterationExtends['iteration_leader']) && $users = array_merge($users, $iterationExtends['iteration_leader']);
                break;
            case EnumModel::BUSINESS_ARCHITECT: // 业务架构师
                isset($iterationExtends['business_architect']) && $users = array_merge($users, $iterationExtends['business_architect']);
                break;
            }
        }

        return $users;
    }

    /**
     * 更新节点
     * @param  int    $iterationProcessNodeId
     * @param  array  $params
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/11/14
     */
    public function updateNode(int $iterationProcessNodeId, array $params)
    {
        // 验证器校验
        validate(IterationProcessNodeValidate::class)->scene('updateNode')->check($params);

        $data = [];

        if ( ! empty($params['extends'])) {
            // 处理不符合条件的节点负责人
            if ($params['extends']['node_manger']['type'] != 'personal' && $params['extends']['node_manger']['users']) {
                // 查询成员角色
                $nodeMangerUser = ProjectUserRoleLogic::selectUserRole($params['extends']['node_manger']['users']);

                // 移除不符合条件的成员
                foreach ($params['extends']['node_manger']['users'] as $k => $user) {
                    if ($nodeMangerUser->where('user_id', '=', $user)->whereIn('project_role', $params['extends']['node_manger']['roles'])->isEmpty()) {
                        unset($params['extends']['node_manger']['users'][$k]);
                    }
                }

                $params['extends']['node_manger']['users'] = array_values($params['extends']['node_manger']['users']);
            }

            // 当角色权限为个人时，清空角色数据
            if (isset($params['extends']['node_manger']['type']) && $params['extends']['node_manger']['type'] == 'personal') {
                $params['extends']['node_manger']['roles'] = [];
            }

            // 更新迭代预设的人员角色数据
            if (empty($params['extends']['node_manger']['users']) && isset($params['is_update_default_user_data']) && $params['is_update_default_user_data']) {
                $params['extends']['node_manger']['users'] = $this->updateDefaultUserData($iterationProcessNodeId, $params['extends']['node_manger']['roles']);
            }

            if ( ! empty($params['extends']['node_manger']['users'])) {
                // 获取节点的需求人为空的任务id集合
                $cntIds = WorkItemsLogic::getIterationNodeTaskIdByNodeId($iterationProcessNodeId, null, null, false);
                if ($cntIds) {
                    WorkItemsLogic::replaceProjectUser($cntIds, 0, $params['extends']['node_manger']['users']);
                }
            }

            $data['node_data'] = $params['extends'];
            $data['node_name'] = $params['extends']['basic']['node_name'] ?? '';

        }
        if ( ! empty($params['estimate_start_time'])) {
            $data['estimate_start_time'] = $params['estimate_start_time'];
        }
        if ( ! empty($params['estimate_end_time'])) {
            $data['estimate_end_time'] = $params['estimate_end_time'];
        }

        IterationProcessNodeLogic::updateNode($iterationProcessNodeId, $data);
    }

    /**
     * 发起审批
     * @param  int     $projectId
     * @param  int     $iterationProcessNodeId
     * @param  string  $remark
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws \Elastic\Elasticsearch\Exception\ClientResponseException
     * @throws \Elastic\Elasticsearch\Exception\ServerResponseException
     * @throws \Throwable
     * User Long
     * Date 2024/11/14
     */
    public function initiate(int $projectId, int $iterationProcessNodeId, string $remark)
    {
        $iterationModel = IterationProcessNodeModel::findById($iterationProcessNodeId);
        if ( ! $iterationModel) {
            throw new NotFoundException();
        }

        if ($iterationModel->status < IterationProcessNodeModel::STATUS_UNDER_WAY) {
            throw new BusinessException('请先开启节点！');
        }

        $error_msg = '';

        // 获取未完成的任务id集合
        $cntIds = WorkItemsLogic::getIterationNodeTaskIdByNodeId($iterationProcessNodeId, false, true);
        if ($cntIds) {
            $error_msg .= '任务';
        }

        if ($iterationModel->is_auto_status == IterationProcessNodeModel::IS_AUTO_STATUS_TURN_DOWN) {
            $error_msg .= ($error_msg ? '、' : '').'自动化检验';
        }

        if ($error_msg) {
            throw new BusinessException($error_msg.' 未完成');
        }

        if ( ! self::isNodeButton($projectId, $iterationModel->node_data['node_manger']['users'] ?? [])) {
            throw new BusinessException('只有负责人、迭代leader、项目经理、有权限人员才可点击');
        }

        try {
            Db::startTrans();

            // 发起审批
            IterationProcessNodeAuditLogic::initiate($iterationProcessNodeId, $remark);

            // 记录节点审批状态
            IterationProcessNodeLogic::updateNode($iterationProcessNodeId, [
                'is_audit_status' => IterationProcessNodeModel::IS_AUDIT_STATUS_PROCEED,
            ]);

            Db::commit();
        } catch (\Throwable $e) {
            Db::rollback();

            throw $e;
        }
    }

    /**
     * 审批
     * @param  int     $projectId
     * @param  int     $iterationProcessNodeId
     * @param  int     $isAudit
     * @param  string  $remark
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws \Throwable
     * User Long
     * Date 2024/11/14
     */
    public function approve(int $projectId, int $iterationProcessNodeId, int $isAudit, string $remark)
    {
        $iterationModel = IterationProcessNodeModel::findById($iterationProcessNodeId);
        if ( ! $iterationModel) {
            throw new NotFoundException();
        }

        $auditRoles = $iterationModel->node_data['node_setting']['audit']['roles'] ?? [];
        $nodeUsers = $iterationModel->node_data['node_setting']['audit']['users'] ?? [];

        $role = self::isNodeButton($projectId, $nodeUsers, $auditRoles);
        if ( ! $role && is_array($role)) {
            if ( ! self::isNodeButton($projectId)) {
                throw new BusinessException('无操作权限');
            }
        } else {
            if ( ! $role) {
                throw new BusinessException('无操作权限');
            }
        }

        try {
            Db::startTrans();

            switch ($isAudit) {
            case IterationProcessNodeAuditModel::PASS:
                // 审批通过
                IterationProcessNodeAuditLogic::pass($iterationProcessNodeId, $remark);

                // 记录节点审批状态
                IterationProcessNodeLogic::updateNode($iterationProcessNodeId, [
                    'is_audit_status' => IterationProcessNodeModel::IS_AUDIT_STATUS_PASS
                ]);
                break;
            case IterationProcessNodeAuditModel::TURN_DOWN:
                // 审批拒绝
                IterationProcessNodeAuditLogic::turnDown($iterationProcessNodeId, $remark);

                // 记录节点审批状态
                IterationProcessNodeLogic::updateNode($iterationProcessNodeId, [
                    'is_audit_status' => IterationProcessNodeModel::IS_AUDIT_STATUS_TURN_DOWN
                ]);

                // 记录 审批拒绝
                IterationProcessNodeTipLogic::turnDown($iterationProcessNodeId);
                break;
            default:
                throw new ParamsException();
            }

            Db::commit();
        } catch (\Throwable $e) {
            Db::rollback();

            throw $e;
        }
    }

    /**
     * 启用节点
     * @param  int  $projectId
     * @param  int  $iterationProcessNodeId
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/11/14
     */
    public function onNode(int $projectId, int $iterationProcessNodeId)
    {
        IterationProcessNodeLogic::onNode($projectId, $iterationProcessNodeId);
    }

    /**
     * 跳过节点
     * @param  int  $projectId
     * @param  int  $iterationProcessNodeId
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/11/14
     */
    public function skipNode(int $projectId, int $iterationProcessNodeId)
    {
        IterationProcessNodeLogic::skipNode($projectId, $iterationProcessNodeId);
    }

    /**
     * 获取迭代目标
     * @param  int  $iterationId
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/11/15
     */
    public function getIterativeGoal(int $iterationId)
    {
        $res = [];
        $model = IterationModel::findById($iterationId);
        if ( ! $model) {
            throw new NotFoundException();
        }

        $res['estimate_start_time'] = $model->estimate_start_time;
        $res['estimate_end_time'] = $model->estimate_end_time;
        $res['start_time'] = $model->start_time;
        $res['end_time'] = $model->end_time;

        // 获取流程节点信息
        $nodeModel = IterationProcessNodeLogic::selectProcessNodeByIterationId($iterationId);

        // 获取所有节点负责人
        $nodeData = $nodeModel->column('node_data');
        $nodeManger = array_column($nodeData, 'node_manger');
        $nodeMangerUserIds = array_unique(array_merge(...array_column($nodeManger, 'users')));

        // 获取迭代 leader
        $iterationLeader = $model['extends']['iteration_leader'] ?? [];

        $res['user_info'] = [];
        // 合并项目所有人员 id 并查询中台信息
        $userIds = array_unique(array_merge($iterationLeader, $nodeMangerUserIds));
        if ($userIds) {
            // 查询迭代成员信息
            $userInfo = ProjectUserLogic::getUserInfoByUserIds((int)$model->project_id, $userIds);
            foreach ($userInfo as $item) {
                foreach ($item['role'] ?? [] as $role) {
                    if ($role->project_role != "Admin") {
                        $res['user_info'][$role->project_role]['role_code'] = $role->project_role;
                        $res['user_info'][$role->project_role]['role_name'] = $role->project_name;
                        $res['user_info'][$role->project_role]['user_list'][] = [
                            'user_id'   => $item->user_id,
                            'user_name' => spliceUserName(
                                $item->en_user_name,
                                $item->user_name,
                                $item->position_name
                            )
                        ];
                    }
                }
            }
        }

        // 节点数据
        $nodeModel->each(function (&$node) {
            $node['node_manger'] = $node->node_data['node_manger']['users_info'] ?? [];
        });
        $res['node_list'] = $nodeModel->hidden(['node_data', '_is_time_convert']);

        return $res;
    }

    /**
     * 完成节点
     * @param  int  $projectId
     * @param  int  $iterationProcessNodeId
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/11/14
     */
    public function completeNode(int $projectId, int $iterationProcessNodeId)
    {
        IterationProcessNodeLogic::completeNode($projectId, $iterationProcessNodeId);
    }

    /**
     * 获取迭代流程状态
     * @param  int  $iterationId
     * @return \app\iterate\model\FlowStatusTextModel[]|array|Collection
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/11/25
     */
    public function selectorFlowStatus(int $iterationId)
    {
        $iterationModel = IterationModel::findById($iterationId);
        if ( ! $iterationModel) {
            return [];
        }

        return FlowStatusTextLogic::selectorFlowStatusByIterationId((int)$iterationModel->flow_status_id);
    }
}
