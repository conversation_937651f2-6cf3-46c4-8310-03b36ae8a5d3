<?php
/**
 * Desc 项目成员 - 逻辑层
 * User Long
 * Date 2024/08/12
 */
declare (strict_types=1);

namespace app\project\logic;

use app\infrastructure\logic\EnumLogic;
use app\infrastructure\logic\PermissionsLogic;
use app\infrastructure\model\EnumModel;
use app\project\model\ProjectUserModel;
use app\project\validate\ProjectUserValidate;
use app\work_items\logic\WorkItemsLogic;
use basic\BaseLogic;
use basic\BaseModel;
use components\platform\user\requests\User;
use components\util\BaseRequest;
use exception\BusinessException;
use exception\NotFoundException;
use think\Collection;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use Throwable;
use utils\Ctx;
use utils\DBTransaction;
use Overtrue\Pinyin\Pinyin;

class ProjectUserLogic extends BaseLogic
{
    // 是否需要清空数据，0-否，1-是
    const EMPTY = 0;
    const NOT_EMPTY = 1;

    /**
     * 校验中台子系统用户状态
     * @param  array  $centerUserData
     * @param  array  $userIds
     * User Long
     * Date 2024/8/31
     */
    private function validateCenterUserData(array $centerUserData, array $userIds): void
    {
        if ( ! $centerUserData) {
            throw new BusinessException('查无该成员，请检查是否已在中台添加用户');
        }
        if (count($centerUserData) != count($userIds)) {
            throw new BusinessException('存在未入职或离职成员，请联系管理员添加');
        }
    }

    /**
     * 校验中台子系统用户入职状态
     * @param  array  $centerUserData
     * User Long
     * Date 2024/12/28
     */
    private function validateIsEnableData(array $centerUserData): void
    {
        foreach ($centerUserData as $detail) {
            if ( ! $detail->is_enable) {
                throw new BusinessException('该成员已离职，请刷新后重新添加');
            }
        }
    }

    /**
     * 获取中台子系统用户数据
     * @param  array   $pageData
     * @param  string  $keyword
     * @param  array   $userIds
     * @param  string  $projectRole
     * @return mixed
     * User Long
     * Date 2024/8/12
     */
    private function getCenterUserPage(array $pageData, string $keyword = '', array $userIds = [], string $projectRole = ''): mixed
    {
        try {
            // 获取子系统id
            $systemId = env('platform.center_sub_system_id');

            $roleCodes = [];
            // 获取前端角色
            if ($projectRole) {
                $roleCodes = [$projectRole];
            }
            // 获取系统角色
            if ( ! $roleCodes) {
                $roleCodes = EnumLogic::getEnumValues(EnumModel::SYSTEM_ROLE);
            }

            // 组合中台数据
            $centerParam = [
                'system_id'  => $systemId,
                'role_codes' => $roleCodes,
                'keyword'    => $keyword,
                'user_ids'   => $userIds,
                'page'       => $pageData['current_page'],
                'list_rows'  => $pageData['per_page']
            ];
            return (new User(Ctx::$token))->getSubSystemRoleUserPage($centerParam)->wait();
        } catch (Throwable $e) {
            throw new BusinessException('网络异常，请刷新重试');
        }
    }

    /**
     * 获取中台子系统用户数据 V2 接口
     * @param  array         $pageData
     * @param  string        $keyword
     * @param  string|array  $projectRole
     * @param  int|array     $userIds
     * @return mixed
     * User Long
     * Date 2024/12/27
     */
    private function getCenterUserPageV2(array $pageData, string $keyword = '', string|array $projectRole = '', int|array $userIds = []): mixed
    {
        try {
            // 获取子系统id
            $systemId = env('platform.center_sub_system_id');

            $roleCodes = [];
            // 获取前端角色
            if ($projectRole) {
                $roleCodes = is_string($projectRole) ? [$projectRole] : $projectRole;
            }
            // 获取系统角色
            if ( ! $roleCodes) {
                $roleCodes = EnumLogic::getEnumValues(EnumModel::SYSTEM_ROLE);
            }

            // 组合中台数据
            $centerParam = [
                'system_id'  => $systemId,
                'role_codes' => $roleCodes,
                'keyword'    => $keyword,
                'user_ids'   => is_array($userIds) ? $userIds : [$userIds],
                'page'       => $pageData['current_page'],
                'list_rows'  => $pageData['per_page']
            ];
            return (new User(Ctx::$token))->getSubSystemRoleUserDropDownPageList($centerParam)->wait();
        } catch (Throwable $e) {
            throw new BusinessException('网络异常，请刷新重试');
        }
    }

    /**
     * 获取中台用户详情
     * @param  int|array  $userIds
     * @return mixed
     * User Long
     * Date 2025/2/28
     */
    private function getCenterUserDetailV2(int|array $userIds = []): mixed
    {
        try {
            // 组合中台数据
            $centerParam = [
                'user_ids'  => is_array($userIds) ? $userIds : [$userIds],
                'page'      => 1,
                'list_rows' => 5000
            ];
            return (new User(Ctx::$token))->getSystemUserDetail($centerParam)->wait();
        } catch (Throwable $e) {
            throw new BusinessException('网络异常，请刷新重试');
        }
    }

    /**
     * 获取中台飞书详情 入参二选一即可
     * * @param  array  $userIds  用户id集
     * * @param  array  $phones  手机号集
     * @return mixed
     * User Long
     * Date 2025/2/27
     */
    private function getFsOpenAndUnionId(array $userIds = [], array $phones = []): mixed
    {
        try {
            return (new User(Ctx::$token))->getFsOpenAndUnionId($userIds, $phones)->wait();
        } catch (Throwable $e) {
            throw new BusinessException('网络异常，请刷新重试');
        }
    }

    /**
     * 获取中台用户列表详情信息
     * @param  array  $userIds
     * @return mixed
     * User Long
     * Date 2024/8/29
     */
    private function getCenterUserListDetailData(array $userIds): mixed
    {
        try {
            return (new User(Ctx::$token))->getUserInfoByCustomer([
                'user_ids' => $userIds
            ])->wait();
        } catch (Throwable $e) {
            throw new BusinessException('网络异常，请刷新重试');
        }
    }

    /**
     * 生成拼音（首拼和全拼）
     * @param  string  $name  中文名称
     * @return array 返回包含首拼和全拼的数组
     */
    private function generatePinyin(string $name): array
    {
        if (empty($name)) {
            return ['', ''];
        }

        $pinyin = new Pinyin();
        // 生成首拼
        $shortPinyin = $pinyin->abbr($name);
        // 生成全拼
        $fullPinyin = $pinyin->sentence($name, '');

        return [str_replace(' ', '', (string)$shortPinyin), str_replace(' ', '', (string)$fullPinyin)];
    }

    /**
     * 检查并更新用户名拼音字段
     * 如果拼音字段为空，则生成并更新
     * @return int 更新的记录数
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function updateUserNamePinyin(): int
    {
        // 获取所有用户记录
        $users = ProjectUserModel::status()->select();
        if ($users->isEmpty()) {
            return 0;
        }

        $updateCount = 0;
        foreach ($users as $user) {
            // 检查是否需要更新拼音
            if (empty($user->name_pinyin_short) || empty($user->name_pinyin_full)) {
                // 生成拼音
                [$shortPinyin, $fullPinyin] = $this->generatePinyin($user->user_name);

                // 更新记录
                $user->name_pinyin_short = $shortPinyin;
                $user->name_pinyin_full = $fullPinyin;
                $user->save();

                $updateCount++;
            }
        }

        return $updateCount;
    }

    /**
     * 组装更新用户数据
     * @param $upData
     * @param $centerDatum
     * @param $item
     * @param $upSave
     * @return array
     * User Long
     * Date 2025/2/28
     */
    private function assembleUpdateData($upData, $centerDatum, $item, $upSave)
    {
        // 中台用户名称与本地不同，更新本地数据
        if ($centerDatum->user_name != $item->user_name) {
            $upData[$item->user_id]['data']['user_name'] = $centerDatum->user_name;

            // 生成并更新拼音
            [$shortPinyin, $fullPinyin] = $this->generatePinyin($centerDatum->user_name);
            $upData[$item->user_id]['data']['name_pinyin_short'] = $shortPinyin;
            $upData[$item->user_id]['data']['name_pinyin_full'] = $fullPinyin;

            $upSave = true;
        }

        // 中台英文用户名称与本地不同，更新本地数据
        if ($centerDatum->en_user_name != $item->en_user_name) {
            $upData[$item->user_id]['data']['en_user_name'] = $centerDatum->en_user_name;
            $upSave = true;
        }

        // 中台飞书头像与本地不同，更新本地数据
        if ($centerDatum->avatar != $item->avatar) {
            $upData[$item->user_id]['data']['avatar'] = $centerDatum->avatar;
            $upSave = true;
        }

        // 中台主岗位名与本地不同，更新本地数据
        if ($centerDatum->position_name != $item->position_name) {
            $upData[$item->user_id]['data']['position_name'] = $centerDatum->position_name;
            $upSave = true;
        }

        // 中台手机号码与本地不同，更新本地数据
        if ($centerDatum->phone != $item->phone) {
            $upData[$item->user_id]['data']['phone'] = $centerDatum->phone;
            $upSave = true;
        }

        if ($upSave) {
            $upData[$item->user_id]['where'] = ['user_id' => $item->user_id, 'is_delete' => BaseModel::DELETE_NOT];
        }

        return $upData;
    }

    /**
     * 新增项目成员
     * @param  int    $projectId
     * @param  array  $userIds
     * @return array|void
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws Throwable
     * User Long
     * Date 2024/8/12
     */
    public function create(int $projectId, array $userIds)
    {
        // 验证器校验
        validate(ProjectUserValidate::class)->scene('create')->check(['project_id' => $projectId, 'user_ids' => $userIds]);

        // 判断是否查询到符合数据，查到直接返回
        $userData = ProjectUserModel::selectUserInfoByProjectIdAndUserIds($projectId, $userIds)->toArray();
        if (count($userData) == count($userIds)) {
            return $userData;
        }

        // 过滤掉已添加的用户
        if ($userData) {
            $userIds = array_values(array_diff($userIds, array_column($userData, 'user_id')));
        }

        $centerUserData = $this->getCenterUserPageV2(['current_page' => 1, 'per_page' => 5000], '', '', $userIds)['data'] ?? [];
        $this->validateCenterUserData($centerUserData, $userIds);

        $centerUserDetailData = $this->getCenterUserDetailV2($userIds);
        $this->validateIsEnableData($centerUserDetailData);

        try {
            DBTransaction::begin();

            // 新增成员
            foreach ($centerUserDetailData as $detail) {
                // 生成拼音
                [$shortPinyin, $fullPinyin] = $this->generatePinyin($detail->user_name);

                $model = new ProjectUserModel();
                $model->project_id = $projectId;
                $model->user_id = $detail->user_id;
                $model->user_name = $detail->user_name;
                $model->name_pinyin_short = $shortPinyin;
                $model->name_pinyin_full = $fullPinyin;
                $model->en_user_name = $detail->en_user_name;
                $model->avatar = $detail->avatar;
                $model->position_name = $detail->position_name;
                $model->phone = $detail->phone;
                $model->save();
            }

            foreach ($centerUserData as $center) {
                // 删除角色与项目人员关系（真删）
                ProjectUserRoleLogic::destroyRoleData((int)$center->user_id);

                // 创建项目角色权限
                ProjectUserRoleLogic::createRoleData((int)$center->user_id, array_column($center->role_code, 'code'));
            }

            // 更新项目人数
            (new ProjectInfoLogic())->updateUserCount($projectId);

            DBTransaction::commit();
        } catch (Throwable $e) {
            DBTransaction::rollback();
            throw $e;
        }
    }

    /**
     * 项目用户列表
     * @param  int     $projectId
     * @param  string  $keyword
     * @param  string  $projectRole
     * @return mixed
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/10/11
     */
    public function pageQuery(int $projectId, string $keyword, string $projectRole = ''): mixed
    {
        $pageData = BaseRequest::setCenterPageData(getPageSize());

        // 获取用户id集
        $userInfo = ProjectUserModel::selectUserInfoByProjectId($projectId, $projectRole);
        if ($userInfo->isEmpty()) {
            return $pageData;
        }
        $userIds = $userInfo->column('user_id');

        // 获取中台数据
        $centerData = $this->getCenterUserPageV2($pageData, $keyword, [], $userIds);
        $detailData = $this->getCenterUserDetailV2($userIds);

        // 组合中台数据
        foreach ($centerData['data'] as $centerDatum) {
            foreach ($detailData as $detailDatum) {
                if (isset($centerDatum->user_id) && $centerDatum->user_id == $detailDatum->user_id) {
                    $centerDatum->addField('is_enable', $detailDatum->is_enable);
                    $centerDatum->addField('enable_name', $detailDatum->enable_name);
                    $centerDatum->addField('position_id', $detailDatum->position_id);
                    $centerDatum->addField('position_name', $detailDatum->position_name);
                    $centerDatum->addField('company_id', $detailDatum->company_id);
                    $centerDatum->addField('company_name', $detailDatum->company_name);
                    $centerDatum->addField('dept_id', $detailDatum->dept_id);
                    $centerDatum->addField('dept_name', $detailDatum->dept_name);
                    $centerDatum->addField('role_name', implode(', ', array_column($centerDatum->role_code, 'roleName')));
                    $centerDatum->addField('role_code', array_column($centerDatum->role_code, 'code'));
                    $centerDatum->addField('avatar', $detailDatum->avatar);
                    $centerDatum->addField('phone', $detailDatum->phone);
                }
            }
        }

        // 中台用户 id 合集
        $centerUserIds = [];

        // 处理中台用户数据同步问题，后续中台提供事件同步结构后调整此段代码
        $centerUserData = $this->getCenterUserPageV2(['current_page' => 1, 'per_page' => 5000], '', [], $userIds)['data'] ?? [];
        foreach ($centerUserData as $centerUserDatum) {
            $centerUserIds[] = $centerUserDatum->user_id ?? 0;
        }

        $upData = [];
        // 赋值本地数据
        foreach ($centerData['data'] as $centerDatum) {
            foreach ($userInfo as $item) {
                if (isset($centerDatum->user_id) && $centerDatum->user_id == $item->user_id) {
                    $centerDatum->addField('create_at', $item->create_at);

                    // 组装更新用户数据
                    $upData = $this->assembleUpdateData($upData, $centerDatum, $item, false);

                    // 处理中台角色不同问题
                    if ($centerDatum->role_code != $item->role->column('project_role')) {
                        // 删除角色与项目人员关系（真删）
                        ProjectUserRoleLogic::destroyRoleData((int)$item->user_id);

                        // 创建项目角色权限
                        ProjectUserRoleLogic::createRoleData((int)$item->user_id, $centerDatum->role_code);
                    }
                }
            }
        }

        if ($upData) {
            foreach ($upData as $upDatum) {
                ProjectUserModel::update($upDatum['data'], $upDatum['where']);
            }
        }

        // 本地数据删除中台移除的用户
        if ($centerUserIds) {
            $isUpdateUserCount = null;
            foreach ($userInfo->where('user_id', 'not in', $centerUserIds) as $model) {
                $model->is_delete = BaseModel::DELETE_YES;
                $model->save();

                $isUpdateUserCount = true;
            }

            // 更新项目人数
            if ($isUpdateUserCount) {
                (new ProjectInfoLogic())->updateUserCount($projectId);
            }
        }

        return $centerData;
    }

    /**
     * 项目用户下拉数据(无分页)
     * @param  int|array  $projectId
     * @param  string     $keyword
     * @param  string     $projectRole
     * @param  array      $projectRoles
     * @param  int        $scenario
     * @param  string     $map
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/11/18
     */
    public function selectorListQuery(int|array $projectId, string $keyword, string $projectRole = '', array $projectRoles = [], int $scenario = 0, string $map = ''): array
    {
        $res = [];

        if ( ! empty($map)) {
            // 枚举使用场景
            if (in_array($map, EnumModel::MAP_HANDLER)) {
                $enumData = EnumLogic::selectorEnumData($map);

                foreach ($enumData as $enumDatum) {
                    $res[] = $this->spliceSelectorData(
                        $enumDatum['enum_value'],
                        $enumDatum['enum_name'],
                        '',
                        '',
                        $enumDatum['enum_name'],
                        $enumDatum['enum_value']
                    );
                }

                return $res;
            }
        }

        // 获取用户id集
        $userInfo = ProjectUserModel::selectUserInfoByProjectId($projectId, array_unique(array_merge($projectRole ? [$projectRole] : [], $projectRoles)), $keyword);

        if ($scenario) {
            $userInfo = ProjectUserModel::selectUserInfoAll();
        }

        if ($userInfo->isEmpty()) {
            return $res;
        }

        foreach ($userInfo as $item) {
            $res[] = $this->spliceSelectorData(
                    $item->user_id,
                    $item->user_name,
                    $item->en_user_name,
                    $item->position_name,
                    spliceUserName($item->en_user_name, $item->user_name, $item->position_name),
                    $item->user_id,
                ) + [
                    'name_pinyin_short' => $item->name_pinyin_short,
                    'name_pinyin_full'  => $item->name_pinyin_full,
                ];
        }

        return $res;
    }

    /**
     * 拼装数据
     * @param $userId
     * @param $userName
     * @param $enUserName
     * @param $positionName
     * @param $label
     * @param $value
     * @return array
     * User Long
     * Date 2025/2/19
     */
    private function spliceSelectorData($userId, $userName, $enUserName, $positionName, $label, $value)
    {
        return [
            'user_id'       => $userId,
            'user_name'     => $userName,
            'en_user_name'  => $enUserName,
            'position_name' => $positionName,
            'label'         => $label,
            'value'         => $value
        ];
    }

    /**
     * 用户下拉数据（中台数据）
     * @param  string  $keyword
     * @param  string  $projectRole
     * @param  int     $projectId
     * @return mixed
     * User Long
     * Date 2024/8/12
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function selectorPageQuery(string $keyword, string $projectRole = '', int $projectId = 0): mixed
    {
        $pageData = BaseRequest::setCenterPageData(getPageSize());

        // 获取用户id集
        $userIds = [];
        if ($projectId) {
            $userInfo = ProjectUserModel::selectUserInfoByProjectId($projectId);

            if ( ! $userInfo->isEmpty()) {
                $userIds = array_values(array_unique($userInfo->column('user_id')));
            }
        }

        // 获取中台数据
        $res = $this->getCenterUserPageV2($pageData, $keyword, $projectRole, $userIds);

        $detailData = [];
        if ($res['data']) {
            $userIds = array_column($res['data'], 'user_id');

            // 查询用户详情
            $detailData = $this->getCenterUserDetailV2($userIds);
        }

        foreach ($res['data'] as $centerDatum) {
            $centerDatum->addField('role_name', implode(',', array_column($centerDatum->role_code, 'roleName')));
            $centerDatum->addField('position_name', '');
            $pinyin = $this->generatePinyin($centerDatum->user_name);
            $centerDatum->addField('name_pinyin_short', $pinyin[0]);
            $centerDatum->addField('name_pinyin_full', $pinyin[1]);
            foreach ($detailData as $detailDatum) {
                if ($centerDatum->user_id == $detailDatum->user_id) {
                    $centerDatum->addField('position_name', $detailDatum->position_name);
                }
            }
        }

        return $res;
    }

    /**
     * 移出成员
     * @param  int  $projectId
     * @param  int  $userId
     * @param  int  $isEmpty
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws Throwable
     * @throws \Elastic\Elasticsearch\Exception\ClientResponseException
     * @throws \Elastic\Elasticsearch\Exception\MissingParameterException
     * @throws \Elastic\Elasticsearch\Exception\ServerResponseException
     * User Long
     * Date 2024/12/7
     */
    public function removeUser(int $projectId, int $userId, int $isEmpty = self::EMPTY)
    {
        // 验证器校验
        validate(ProjectUserValidate::class)->scene('removeUser')->check(['project_id' => $projectId, 'user_id' => $userId, 'is_empty' => $isEmpty]);

        $model = ProjectUserModel::findProjectUserById($projectId, $userId);

        if ( ! $model) {
            throw new NotFoundException();
        }

        try {
            DBTransaction::begin();

            // 移出项目成员为完成的《需求，缺陷，任务》
            if ($isEmpty) {
                WorkItemsLogic::removeProjectUser($projectId, $userId);
            }

            $model->save(['is_delete' => BaseModel::DELETE_YES]);

            // 更新项目人数
            (new ProjectInfoLogic())->updateUserCount($projectId);

            DBTransaction::commit();
        } catch (Throwable $e) {
            DBTransaction::rollback();
            throw $e;
        }
    }

    /**
     * 获取用户信息
     * @param  int    $projectId
     * @param  array  $userIds
     * @return ProjectUserModel[]|array|Collection
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/12/6
     */
    public static function getUserInfoByUserIds(int $projectId, array $userIds)
    {
        return ProjectUserModel::selectUserInfoByProjectIdAndUserIds($projectId, $userIds);
    }

    /**
     * 根据 项目id、角色 删除数据
     * @param  int     $projectId
     * @param  string  $projectRole
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/10/11
     */
    public static function deleteByProjectRole(int $projectId, string $projectRole = ''): void
    {
        $models = ProjectUserModel::selectUserInfoByProjectId($projectId, $projectRole);

        foreach ($models as $model) {
            $model->save(['is_delete' => BaseModel::DELETE_YES]);
        }
    }

    /**
     * 根据 项目id、角色 返回查询数量
     * @param  int     $projectId
     * @param  string  $projectRole
     * @return int
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/10/11
     */
    public static function countByProjectId(int $projectId, string $projectRole = ''): int
    {
        $models = ProjectUserModel::selectUserInfoByProjectId($projectId, $projectRole);
        return $models->count();
    }

    /**
     * 根据项目id、角色 返回查询数据
     * @param  int     $projectId
     * @param  string  $projectRole
     * @return Collection|array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/10/11
     */
    public static function selectByProjectId(int $projectId, string $projectRole = ''): Collection|array
    {
        return ProjectUserModel::selectUserInfoByProjectId($projectId, $projectRole);
    }

    /**
     * 获取当前登录用户参与的项目 id集合
     * @return array|true
     * User Long
     * Date 2024/12/20
     */
    public static function getProjectIdsByUserId()
    {
        // 获取人员权限
        $permissions = PermissionsLogic::getRulePathPermissions();

        // 管理员直接通过
        if ($permissions['is_admin']) {
            return true;
        }

        return ProjectUserModel::status()->where(['user_id' => Ctx::$userId])->column('project_id');
    }

    /**
     * 获取中台飞书openId
     * @param  array  $userIds
     * @return array
     * User Long
     * Date 2025/2/27
     */
    public static function getFsOpenIdByUserIds(array $userIds = [])
    {
        $fsOpenIds = [];

        $fsInfo = (new self())->getFsOpenAndUnionId($userIds);

        foreach ($fsInfo as $fs) {
            $fsOpenIds[] = $fs->fs_login_open_id;
        }

        return $fsOpenIds;
    }

    /**
     * 获取手机号码
     * @param  array  $userIds
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2025/2/28
     */
    public static function getPhoneByUserIds(array $userIds = [])
    {
        $res = [];

        // 获取用户id集
        $userInfo = ProjectUserModel::selectListById($userIds, null, ['user_id']);
        if ($userInfo->isEmpty()) {
            return $res;
        }

        // 获取中台数据
        $detailData = (new self())->getCenterUserDetailV2($userIds);

        $upData = [];
        // 赋值本地数据
        foreach ($detailData as $centerDatum) {
            foreach ($userInfo as $item) {
                if (isset($centerDatum->user_id) && $centerDatum->user_id == $item->user_id) {
                    // 组装更新用户数据
                    $upData = (new self())->assembleUpdateData($upData, $centerDatum, $item, false);

                    $res[] = [
                        'phone'   => $centerDatum->phone,
                        'user_id' => $centerDatum->user_id
                    ];
                }
            }
        }

        if ($upData) {
            foreach ($upData as $upDatum) {
                ProjectUserModel::update($upDatum['data'], $upDatum['where']);
            }
        }

        return $res;
    }
}
