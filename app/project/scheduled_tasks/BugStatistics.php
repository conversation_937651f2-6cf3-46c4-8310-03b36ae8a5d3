<?php

namespace app\project\scheduled_tasks;

use app\iterate\logic\FlowProcessLogic;
use app\iterate\model\FlowStatusEnumModel;
use app\project\logic\IterationCatalogLogic;
use app\project\logic\NissinBugStatisticsRulesLogic;
use app\project\model\BugStatisticsDetailModel;
use app\project\model\BugStatisticsModel;
use app\project\model\ProjectModel;
use app\project\model\TaskRecordModel;
use app\work_items\logic\WorkItemsEsLogic;
use app\work_items\model\WorkItemsModel;
use basic\BaseModel;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat\Wizard\DateTime;
use think\Collection;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\facade\Db;
use utils\Log;

class BugStatistics
{
    /**
     * 查询已开启的项目
     * @return array 已开启的项目列表
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public static function getEnabledProjects(): array
    {
        // 查询未删除且状态为进行中(project_status=1)的项目
        return ProjectModel::where('is_delete', 0)
            ->where('project_status', 1)
            ->select()
            ->toArray();
    }

    /**
     * 统计bug数量并保存到数据库
     * @return void
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public static function statistics(): void
    {
        $nissinBugStatisticsRulesLogic = new NissinBugStatisticsRulesLogic();

        // 获取所有已开启规则的项目
        $projects = self::getEnabledProjects();
        foreach ($projects as $project) {
            // 根据统计日期设置时间范围
            $today = date('Y-m-d');

            // 获取项目日清bug统计规则
            $rule = $nissinBugStatisticsRulesLogic->getByProjectIdDefault($project['project_id']);

            if ($rule['enable'] != 1) {
                continue;
            }

            if ($rule['statistics'] == 2) { // 昨天
                $today = date('Y-m-d', strtotime('-1 day'));
            }

            // 判断是否为工作日,非工作日则跳过统计
            if ( ! (new  NissinBugStatisticsRulesLogic())->isWorkingDayByProjectId((int)$project['project_id'], $today)) {
                continue;
            }

            // 检查任务记录表是否已存在记录，存在则跳过统计
            $taskRecord = TaskRecordModel::findByProjectIdAndDate($project['project_id'], $today);
            if ($taskRecord) {
                continue; // 跳过后续统计
            }

            // 获取项目定时器执行时间
            $defaultBugLiquidationTime = $rule['time'];

            // 判断当前时间是否大于执行时间
            if (date('H:i:s') <= $defaultBugLiquidationTime) {
                continue;
            }

            // 先创建任务记录，防止重复执行
            $taskRecordId = self::createTaskRecord($project['project_id'], $today, $rule['statistics'], $defaultBugLiquidationTime);

            // 使用事务包裹后续操作
            Db::startTrans();
            try {
                // 获取项目下所有进行中的迭代
                $iterationCatalogLogic = new IterationCatalogLogic();
                $iterations = $iterationCatalogLogic->listQuery($project['project_id'], [], false);

                if ( ! $iterations) {
                    Db::commit();
                    continue; // 如果没有迭代，跳过
                }

                $totalBugCount = 0;

                // 按迭代分别统计
                foreach ($iterations as $iteration) {
                    // 获取迭代关联的流程信息
                    $flowProcess = FlowProcessLogic::findProcessById($iteration['flow_process_id']);
                    if ( ! $flowProcess) {
                        continue;
                    }

                    // 获取bug清算时间
                    $bugLiquidationTime = $flowProcess->bug_liquidation_time;
                    if ( ! $bugLiquidationTime) {
                        continue;
                    }

                    // 构建查询条件 - 统计bug_liquidation_time时间之前的未结束的bug数量
                    $statisticsDate = $today.' '.$bugLiquidationTime; // 统计截止时间点


                    $statusEnumIdList = FlowStatusEnumModel::getUnhandledDefectStatusCollectionByProjectId();

                    $where = [
                        ['field_name' => 'project_id', 'value' => $project['project_id'], 'type' => 'term', 'operate_type' => 'equal'],
                        ['field_name' => 'iteration_id', 'value' => $iteration['iteration_id'], 'type' => 'term', 'operate_type' => 'equal'],
                        ['field_name' => 'cnt_type', 'value' => WorkItemsModel::CNT_TYPE_FLAW, 'type' => 'term', 'operate_type' => 'equal'],
                        ['field_name' => 'is_delete', 'value' => 0, 'type' => 'term', 'operate_type' => 'equal'],
//                        ['field_name' => 'isEnd', 'value' => false, 'type' => 'term', 'operate_type' => 'equal'], // 未结束的bug
                        ['field_name' => 'status_enum_id', 'value' => $statusEnumIdList, 'type' => 'selector'], // 未结束的bug
                        ['field_name' => 'create_at', 'value' => ['', $statisticsDate], 'type' => 'date'] // 创建时间早于统计截止时间
                    ];


                    // 获取bug列表和总数
                    $bugResult = WorkItemsEsLogic::getInstance()->esSearch($where, WorkItemsEsLogic::NOT_PAGE_MAX);

                    $bugCount = $bugResult->total();
                    if ($bugCount > 0) {
                        $bugList = $bugResult->getCollection()->toArray();

                        // 保存迭代的bug详情
                        self::saveIterationBugDetails($project['project_id'], $iteration['iteration_id'], $bugList, $today, $rule['statistics'], $bugLiquidationTime);

                        // 按用户统计当前迭代的bug数量
                        self::saveUserBugStatisticsByIteration($project['project_id'], $iteration['iteration_id'], $bugList, $today, $rule['statistics'], $bugLiquidationTime);
                    }

                    $totalBugCount += $bugCount;
                }

                // 更新任务记录的bug数量
                self::updateTaskRecordCount($taskRecordId, $totalBugCount);

                // 提交事务
                Db::commit();
            } catch (\Throwable $e) {
                // 回滚事务
                Db::rollback();

                // 删除任务记录，允许下次重新执行
                self::deleteTaskRecord($taskRecordId);

                // 记录错误日志
                Log::instance(Log::PLATFORM)->error('Bug统计失败，项目ID：'.$project['project_id'].'，日期：'.$today.'，错误：'.$e->getMessage().'，错误行数：'.$e->getLine());
                Log::instance(Log::PLATFORM)->error((string)$e);

                // 继续处理下一个项目
                continue;
            }
        }

    }

    /**
     * 按日期区间重新统计Bug数量并保存到数据库
     * @param  string  $startDate  开始日期 (Y-m-d)
     * @param  string  $endDate    结束日期 (Y-m-d)
     * @return void
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws \Exception
     */
    public static function regenerateStatisticsByDateRange(string $startDate, string $endDate): void
    {

        $nissinBugStatisticsRulesLogic = new NissinBugStatisticsRulesLogic();
        $projects = self::getEnabledProjects();

//        $projects=(new Collection($projects))->where('project_id',23)->toArray();


        $period = new \DatePeriod(
            new \DateTime($startDate),
            new \DateInterval('P1D'),
            (new \DateTime($endDate))->modify('+1 day') // Ensure endDate is inclusive
        );

        foreach ($period as $date) {

            $date = $date->format('Y-m-d');
            // 获取所有已开启规则的项目
            foreach ($projects as $project) {

                // 获取项目日清bug统计规则
                $rule = $nissinBugStatisticsRulesLogic->getByProjectIdDefault($project['project_id']);

                if ($rule['enable'] != 1) {
                    continue;
                }
                $today = $date;
                if ($rule['statistics'] == 2) { // 昨天
                    $today = (new \DateTime($today))->modify('-1 day')->format('Y-m-d');
                }

                // 判断是否为工作日,非工作日则跳过统计
                if ( ! (new  NissinBugStatisticsRulesLogic())->isWorkingDayByProjectId((int)$project['project_id'], $today)) {
                    continue;
                }
//                dd($today);
//                // 检查任务记录表是否已存在记录，存在则跳过统计
//                $taskRecord = TaskRecordModel::findByProjectIdAndDate($project['project_id'], $today);
//                if ($taskRecord) {
//                    continue; // 跳过后续统计
//                }

                // 获取项目定时器执行时间
                $defaultBugLiquidationTime = $rule['time'];

                // 判断当前时间是否大于执行时间
                if (date('Y-m-d H:i:s') <= $date.' '.$defaultBugLiquidationTime) {
                    continue;
                }

                // 使用事务包裹后续操作
                Db::startTrans();
                try {


                    // 清理指定项目和日期的旧统计数据
                    TaskRecordModel::where('project_id', $project['project_id'])->where('date', $today)->save(['is_delete' => BaseModel::DELETE_YES]);
                    BugStatisticsDetailModel::where('project_id', $project['project_id'])->where('date', $today)->save(['is_delete' => BaseModel::DELETE_YES]);
                    BugStatisticsModel::where('project_id', $project['project_id'])->where('date', $today)->save(['is_delete' => BaseModel::DELETE_YES]);


                    // 先创建任务记录，防止重复执行
                    $taskRecordId = self::createTaskRecord($project['project_id'], $today, $rule['statistics'], $defaultBugLiquidationTime);


                    // 获取项目下所有进行中的迭代
                    $iterationCatalogLogic = new IterationCatalogLogic();
                    $iterations = $iterationCatalogLogic->listQuery($project['project_id']);


//                    $iterations=(new Collection($iterations))->where('iteration_id',63)->toArray();

                    if ( ! $iterations) {
                        Db::commit();
                        continue; // 如果没有迭代，跳过
                    }

                    $totalBugCount = 0;

                    // 按迭代分别统计
                    foreach ($iterations as $iteration) {
                        // 获取迭代关联的流程信息
                        $flowProcess = FlowProcessLogic::findProcessById($iteration['flow_process_id']);
                        if ( ! $flowProcess) {
                            continue;
                        }

                        // 获取bug清算时间
                        $bugLiquidationTime = $flowProcess->bug_liquidation_time;
                        if ( ! $bugLiquidationTime) {
                            continue;
                        }

                        // 构建查询条件 - 统计bug_liquidation_time时间之前的未结束的bug数量
                        $statisticsDate = $today.' '.$bugLiquidationTime; // 创建时间截止时间
                        $statisticsDate2 = $date.' '.$defaultBugLiquidationTime; //解决时间截止时间


//                        $statusEnumIdList = FlowStatusEnumModel::getUnhandledDefectStatusCollectionByProjectId();

                        $where = [
                            ['field_name' => 'project_id', 'value' => $project['project_id'], 'type' => 'term', 'operate_type' => 'equal'],
                            ['field_name' => 'iteration_id', 'value' => $iteration['iteration_id'], 'type' => 'term', 'operate_type' => 'equal'],
                            ['field_name' => 'cnt_type', 'value' => WorkItemsModel::CNT_TYPE_FLAW, 'type' => 'term', 'operate_type' => 'equal'],
                            ['field_name' => 'is_delete', 'value' => 0, 'type' => 'term', 'operate_type' => 'equal'],
//                        ['field_name' => 'isEnd', 'value' => false, 'type' => 'term', 'operate_type' => 'equal'], // 未结束的bug
//                            ['field_name' => 'status_enum_id', 'value' => $statusEnumIdList, 'type' => 'selector'], // 未结束的bug
                            ['field_name' => 'create_at', 'value' => ['', $statisticsDate], 'type' => 'date'],// 创建时间早于统计截止时间
                            ['field_name' => 'resolution_time', 'value' => [$statisticsDate2, ''], 'type' => 'date'], // 解决时间晚于定时器执行时间，无解决时间的不会计入
                        ];

                        // 获取bug列表和总数
                        $bugResult = WorkItemsEsLogic::getInstance()->esSearch($where, WorkItemsEsLogic::NOT_PAGE_MAX);
//                        dd($bugResult->column('cnt_id'));

                        $bugCount = $bugResult->total();
                        if ($bugCount > 0) {
                            $bugList = $bugResult->getCollection()->toArray();

                            foreach ($bugList as &$bug) {
                                $bug['handler_uid'] = $bug['developer_uid'] ?? $bug['handler_uid'] ?? [];
                            }


                            // 保存迭代的bug详情
                            self::saveIterationBugDetails($project['project_id'], $iteration['iteration_id'], $bugList, $today, $rule['statistics'], $bugLiquidationTime);

                            // 按用户统计当前迭代的bug数量
                            self::saveUserBugStatisticsByIteration($project['project_id'], $iteration['iteration_id'], $bugList, $today, $rule['statistics'], $bugLiquidationTime);
                        }

                        $totalBugCount += $bugCount;
                    }

                    // 更新任务记录的bug数量
                    self::updateTaskRecordCount($taskRecordId, $totalBugCount);

                    // 提交事务
                    Db::commit();
                } catch (\Throwable $e) {
                    // 回滚事务
                    Db::rollback();

                    // 记录错误日志
                    Log::instance(Log::PLATFORM)->error('Bug统计失败，项目ID：'.$project['project_id'].'，日期：'.$today.'，错误：'.$e->getMessage().'，错误行数：'.$e->getLine());
                    Log::instance(Log::PLATFORM)->error((string)$e);

                    // 继续处理下一个项目
                    throw $e;
                }
            }
        }
        Log::instance(Log::PLATFORM)->info('所有指定日期区间的Bug统计重新生成完成');
    }

    /**
     * 创建任务记录
     * @param  int     $projectId   项目ID
     * @param  string  $date        日期
     * @param  int     $statistics  统计类型：1当天、2昨天
     * @param  string  $time        bug清算时间
     * @return int 任务记录ID
     */
    private static function createTaskRecord(int $projectId, string $date, int $statistics, string $time): int
    {
        // 创建新记录
        $record = TaskRecordModel::create([
            'project_id' => $projectId,
            'date'       => $date,
            'time'       => $time,
            'statistics' => $statistics,
            'count'      => 0 // 初始化为0，后续更新
        ]);

        return $record->id;
    }

    /**
     * 更新任务记录的bug数量
     * @param  int  $recordId  任务记录ID
     * @param  int  $count     bug数量
     * @return void
     */
    private static function updateTaskRecordCount(int $recordId, int $count): void
    {
        TaskRecordModel::where('id', $recordId)->update(['count' => $count]);
    }

    /**
     * 删除任务记录
     * @param  int  $recordId  任务记录ID
     * @return void
     */
    private static function deleteTaskRecord(int $recordId): void
    {
        TaskRecordModel::where('id', $recordId)->delete();
    }

    /**
     * 保存任务记录
     * @param  int     $projectId   项目ID
     * @param  string  $date        日期
     * @param  int     $statistics  统计类型：1当天、2昨天
     * @param  string  $time        bug清算时间
     * @param  int     $count       缺陷数量
     * @return void
     */
    private static function saveTaskRecord(int $projectId, string $date, int $statistics, string $time, int $count): void
    {
        // 检查是否已存在记录
        $record = TaskRecordModel::findByProjectIdAndDate($projectId, $date);

        if ($record) {
            // 更新记录
            $record->save([
                'time'       => $time,
                'statistics' => $statistics,
                'count'      => $count
            ]);
        } else {
            // 创建新记录
            TaskRecordModel::create([
                'project_id' => $projectId,
                'date'       => $date,
                'time'       => $time,
                'statistics' => $statistics,
                'count'      => $count
            ]);
        }
    }

    /**
     * 保存迭代的bug详情
     * @param  int     $projectId    项目ID
     * @param  int     $iterationId  迭代ID
     * @param  array   $bugList      bug列表
     * @param  string  $date         日期
     * @param  int     $statistics   统计类型：1当天、2昨天
     * @param  string  $time         bug清算时间
     * @return void
     */
    private static function saveIterationBugDetails(int $projectId, int $iterationId, array $bugList, string $date, int $statistics, string $time): void
    {
        foreach ($bugList as $bug) {
            $userIds = $bug['handler_uid'] ?? 0;
            if (empty($userIds)) {
                $userIds = 0;
            }
            $bugId = $bug['cnt_id'] ?? 0;

            // 如果handler_uid是数组,遍历每个用户ID
            if (is_array($userIds)) {
                foreach ($userIds as $userId) {
                    // 跳过无效的用户ID
                    if ($userId <= 0) {
                        continue;
                    }

                    // 记录bug详情
                    if ( ! BugStatisticsDetailModel::checkBugExists($projectId, $bugId, $date)) {
                        BugStatisticsDetailModel::create([
                            'project_id'   => $projectId,
                            'iteration_id' => $iterationId,
                            'user_id'      => $userId,
                            'date'         => $date,
                            'time'         => $time,
                            'statistics'   => $statistics,
                            'bug_id'       => $bugId
                        ]);
                    }
                }
            } else {
                // 跳过无效的用户ID
                if ($userIds <= 0) {
                    continue;
                }

                // 单个用户ID的情况
                // 记录bug详情
                if ( ! BugStatisticsDetailModel::checkBugExists($projectId, $bugId, $date)) {
                    BugStatisticsDetailModel::create([
                        'project_id'   => $projectId,
                        'iteration_id' => $iterationId,
                        'user_id'      => $userIds,
                        'date'         => $date,
                        'time'         => $time,
                        'statistics'   => $statistics,
                        'bug_id'       => $bugId
                    ]);
                }
            }
        }
    }

    /**
     * 按迭代和用户统计bug数量
     * @param  int     $projectId    项目ID
     * @param  int     $iterationId  迭代ID
     * @param  array   $bugList      bug列表
     * @param  string  $date         日期
     * @param  int     $statistics   统计类型：1当天、2昨天
     * @param  string  $time         bug清算时间
     * @return void
     */
    private static function saveUserBugStatisticsByIteration(int $projectId, int $iterationId, array $bugList, string $date, int $statistics, string $time): void
    {
        // 按用户分组
        $userStats = [];

        foreach ($bugList as $bug) {
            $userIds = $bug['handler_uid'] ?? 0;
            if (empty($userIds)) {
                $userIds = 0;
            }

            // 如果handler_uid是数组,遍历每个用户ID
            if (is_array($userIds)) {
                foreach ($userIds as $userId) {
                    // 跳过未分配用户的bug
                    if ($userId <= 0) {
                        continue;
                    }

                    // 按用户统计
                    if ( ! isset($userStats[$userId])) {
                        $userStats[$userId] = 0;
                    }
                    $userStats[$userId]++;
                }
            } else {
                // 单个用户ID的情况
                // 跳过未分配用户的bug
                if ($userIds <= 0) {
                    continue;
                }

                // 按用户统计
                if ( ! isset($userStats[$userIds])) {
                    $userStats[$userIds] = 0;
                }
                $userStats[$userIds]++;
            }
        }

        // 保存用户在当前迭代的bug统计
        foreach ($userStats as $userId => $count) {
            self::saveBugStatisticsRecord($projectId, $iterationId, $userId, $date, $statistics, $time, $count);
        }
    }

    /**
     * 保存缺陷统计记录
     * @param  int     $projectId    项目ID
     * @param  int     $iterationId  迭代ID
     * @param  int     $userId       用户ID
     * @param  string  $date         日期
     * @param  int     $statistics   统计类型：1当天、2昨天
     * @param  string  $time         bug清算时间
     * @param  int     $count        缺陷数量
     * @return void
     */
    private static function saveBugStatisticsRecord(int $projectId, int $iterationId, int $userId, string $date, int $statistics, string $time, int $count): void
    {
        // 构建查询条件
        $where = [
            'project_id'   => $projectId,
            'iteration_id' => $iterationId,
            'user_id'      => $userId,
            'date'         => $date,
            'is_delete'    => BugStatisticsModel::DELETE_NO
        ];

        // 检查是否已存在记录
        $record = BugStatisticsModel::where($where)->find();

        if ($record) {
            // 更新记录
            $record->save([
                'time'       => $time,
                'statistics' => $statistics,
                'count'      => $count
            ]);
        } else {
            // 创建新记录
            BugStatisticsModel::create([
                'project_id'   => $projectId,
                'iteration_id' => $iterationId,
                'user_id'      => $userId,
                'date'         => $date,
                'time'         => $time,
                'statistics'   => $statistics,
                'count'        => $count
            ]);
        }
    }
}
