<?php
/**
 * Desc 迭代目录
 * User Long
 * Date 2024/11/8
 */

namespace app\project\controller;

use app\project\logic\IterationCatalogLogic;
use resp\Result;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\Request;
use think\response\Json;

class IterationCatalog
{
    private IterationCatalogLogic $logic;

    public function __construct()
    {
        $this->logic = new IterationCatalogLogic();
    }

    /**
     * 项目icon
     * @return Json
     * User Long
     * Date 2024/11/11
     */
    public function getIconPath()
    {
        return Result::success($this->logic->getIconPath());
    }

    /**
     * 新增
     * @param Request $request
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws \Throwable
     * User Long
     * Date 2024/11/11
     */
    public function create(Request $request): Json
    {
        $params = $request->post([
            'project_id',
            'project_category_settings_id',
            'iteration_name',
            'iteration_icon',
            'extends',
            'estimate_start_time',
            'estimate_end_time',
            'start_time',
            'end_time'
        ]);

        $this->logic->create($params);

        return Result::success();
    }

    /**
     * 删除
     * @param Request $request
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/11/11
     */
    public function delete(Request $request): Json
    {
        $iterationIds = $request->post('iteration_ids/a');

        $this->logic->delete($iterationIds);

        return Result::success();
    }

    /**
     * 更新
     * @param Request $request
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/11/11
     */
    public function update(Request $request): Json
    {
        $params = $request->post([
            'iteration_id',
            'iteration_name',
            'iteration_icon',
            'extends',
            'estimate_start_time',
            'estimate_end_time'
        ]);

        $this->logic->update((int)$params['iteration_id'], $params);

        return Result::success();
    }

    /**
     * 详情
     * @param Request $request
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/11/11
     */
    public function detail(Request $request): Json
    {
        $iterationId = $request->get('iteration_id/d');

        return Result::success($this->logic->detail($iterationId));
    }

    /**
     * 列表
     * @param Request $request
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/11/11
     */
    public function listQuery(Request $request): Json
    {
        $projectId = $request->post('project_id/d', 0);
        $searchParams = $request->post('search_params/a', []);
        $isOff = $request->post('is_off/b', null);

        return Result::success($this->logic->listQuery($projectId, $searchParams, $isOff));
    }

    /**
     * 迭代下拉数据
     * @param Request $request
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/11/14
     */
    public function selectorIteration(Request $request): Json
    {
        $projectId = convertProjectId($request->post('project_id'));
        $scenario = $request->post('scenario/d', 0);

        $res = $this->logic->selectorIteration($projectId, $scenario);

        return Result::success($res);
    }

    /**
     * 获取迭代流程节点
     * @param Request $request
     * @return Json
     * User Long
     * Date 2024/12/26
     */
    public function selectorProcessNode(Request $request): Json
    {
        $iterationId = $request->get('iteration_id/d', 0);

        $res = $this->logic->selectorProcessNode($iterationId);

        return Result::success($res);
    }


    /**
     * 获取迭代流程图
     * @param Request $request
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/11/12
     */
    public function getIterationProcessNode(Request $request)
    {
        $iterationId = $request->get('iteration_id/d');

        return Result::success($this->logic->getIterationProcessNode($iterationId));
    }

    /**
     * 获取迭代流程节点详情
     * @param Request $request
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/11/13
     */
    public function getIterationProcessNodeDetail(Request $request)
    {
        $iterationProcessNodeId = $request->get('iteration_process_node_id/d');

        return Result::success($this->logic->getIterationProcessNodeDetail($iterationProcessNodeId));
    }

    /**
     * 编辑节点
     * @param Request $request
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/11/14
     */
    public function updateNode(Request $request)
    {
        $params = $request->post([
            'iteration_process_node_id',
            'extends',
            'estimate_start_time',
            'estimate_end_time',
            'is_update_default_user_data' => false
        ]);

        $this->logic->updateNode((int)$params['iteration_process_node_id'], $params);

        return Result::success();
    }

    /**
     * 发起审批
     * @param Request $request
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws \Throwable
     * User Long
     * Date 2024/11/14
     */
    public function initiate(Request $request)
    {
        $projectId = $request->post('project_id/d');
        $iterationProcessNodeId = $request->post('iteration_process_node_id/d');
        $remark = $request->post('remark/s');

        $this->logic->initiate($projectId, $iterationProcessNodeId, $remark);

        return Result::success();
    }

    /**
     * 审批
     * @param Request $request
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws \Throwable
     * User Long
     * Date 2024/11/14
     */
    public function approve(Request $request)
    {
        $projectId = $request->post('project_id/d');
        $iterationProcessNodeId = $request->post('iteration_process_node_id/d');
        $isAudit = $request->post('is_audit/d');
        $remark = $request->post('remark/s');

        $this->logic->approve($projectId, $iterationProcessNodeId, $isAudit, $remark);

        return Result::success();
    }

    /**
     * 启用节点
     * @param Request $request
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/11/14
     */
    public function onNode(Request $request)
    {
        $projectId = $request->post('project_id/d');
        $iterationProcessNodeId = $request->post('iteration_process_node_id/d');

        $this->logic->onNode($projectId, $iterationProcessNodeId);

        return Result::success();
    }

    /**
     * 跳过节点
     * @param Request $request
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/11/14
     */
    public function skipNode(Request $request)
    {
        $projectId = $request->post('project_id/d');
        $iterationProcessNodeId = $request->post('iteration_process_node_id/d');

        $this->logic->skipNode($projectId, $iterationProcessNodeId);

        return Result::success();
    }

    /**
     * 完成节点
     * @param Request $request
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/11/14
     */
    public function completeNode(Request $request)
    {
        $projectId = $request->post('project_id/d');
        $iterationProcessNodeId = $request->post('iteration_process_node_id/d');

        $this->logic->completeNode($projectId, $iterationProcessNodeId);

        return Result::success();
    }

    /**
     * 获取迭代目标
     * @param Request $request
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/11/15
     */
    public function getIterativeGoal(Request $request)
    {
        $iterationId = $request->get('iteration_id/d');

        return Result::success($this->logic->getIterativeGoal($iterationId));
    }

    /**
     * 更新迭代时间
     * @param Request $request
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/11/14
     */
    public function updateIterativeTime(Request $request)
    {
        $params = $request->post([
            'iteration_id',
            'estimate_start_time',
            'estimate_end_time'
        ]);

        $this->logic->updateIterativeTime((int)$params['iteration_id'], $params);

        return Result::success();
    }

    /**
     * 迭代状态下拉接口
     * @param Request $request
     * @return Json
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/11/25
     */
    public function selectorFlowStatus(Request $request)
    {
        $iterationId = $request->get('iteration_id/d');

        return Result::success($this->logic->selectorFlowStatus($iterationId));
    }
}
