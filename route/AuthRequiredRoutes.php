<?php
/**
 * Desc 路由管理（需要授权）
 * User Long
 * Date 2024/7/2
 */

use middleware\AuthTokenMiddleware;
use middleware\PermissionMiddleware;
use middleware\SaveUrlParams;
use middleware\TrimMiddleware;
use think\facade\Route;

Route::group(function () {
    //产品
    Route::post('product/create', '\app\product\controller\Product@create'); // 新增
    Route::post('product/update', '\app\product\controller\Product@update'); // 更新
    Route::post('product/delete', '\app\product\controller\Product@delete'); // 删除
    Route::post('product/enable', '\app\product\controller\Product@enable'); // 状态启用
    Route::post('product/disable', '\app\product\controller\Product@disable'); // 状态禁用

    //微服务
    Route::post('microservice/create', '\app\microservice\controller\Microservice@create');
    Route::post('microservice/update', '\app\microservice\controller\Microservice@update');
    Route::post('microservice/delete', '\app\microservice\controller\Microservice@delete');
    Route::post('microservice/enable', '\app\microservice\controller\Microservice@enable');
    Route::post('microservice/disable', '\app\microservice\controller\Microservice@disable');

    //通用模板
    Route::post('template/create', '\app\infrastructure\controller\Template@create');
    Route::post('template/update', '\app\infrastructure\controller\Template@update');
    Route::post('template/delete', '\app\infrastructure\controller\Template@delete');
    Route::post('template/copy', '\app\infrastructure\controller\Template@copy');

    // 通用字段配置
    Route::post('fieldConfig/create', '\app\infrastructure\controller\FieldConfig@create');
    Route::post('fieldConfig/update', '\app\infrastructure\controller\FieldConfig@update');
    Route::post('fieldConfig/delete', '\app\infrastructure\controller\FieldConfig@delete');
    Route::post('fieldConfig/fieldCopyTo', '\app\infrastructure\controller\FieldConfig@fieldCopyTo'); // 自定义复制至项目/模板

    // 项目模板
    Route::post('project/template/create', '\app\project\controller\ProjectTemplate@create'); // 新增
    Route::post('project/template/delete', '\app\project\controller\ProjectTemplate@delete'); // 删除
    Route::post('project/template/update', '\app\project\controller\ProjectTemplate@update'); // 更新

    // 项目
    Route::post('project/projectInfo/create', '\app\project\controller\ProjectInfo@create'); // 新增
    Route::post('project/projectInfo/delete', '\app\project\controller\ProjectInfo@delete'); // 删除
    Route::post('project/projectInfo/update', '\app\project\controller\ProjectInfo@update'); // 更新
    Route::post('project/projectInfo/enable', '\app\project\controller\ProjectInfo@enable'); // 状态启用
    Route::post('project/projectInfo/disable', '\app\project\controller\ProjectInfo@disable'); // 状态禁用
    Route::post('project/projectInfo/directQuitProjectUser', '\app\project\controller\ProjectInfo@directQuitProjectUser'); // 退出项目用户（无需交接）
    Route::post('project/projectInfo/upIsTemp', '\app\project\controller\ProjectInfo@upIsTemp'); // 更改项目是否为模板（不对外使用）

    //工作项
    Route::post('workItems/create', '\app\work_items\controller\WorkItems@create'); // 创建
    Route::post('workItems/update', '\app\work_items\controller\WorkItems@update'); // 修改
    Route::post('workItems/delete', '\app\work_items\controller\WorkItems@delete'); // 删除
    Route::post('workItems/batchDelete', '\app\work_items\controller\WorkItems@batchDelete'); // 批量删除
    Route::post('workItems/batchStatusTransfer', '\app\work_items\controller\WorkItems@batchStatusTransfer'); // 批量状态流转
    Route::post('workItems/batchUpdate', '\app\work_items\controller\WorkItems@batchUpdate'); // 批量更新
    Route::post('workItems/batchUpdateCategory', '\app\work_items\controller\WorkItems@batchUpdateCategory'); // 批量更新类别
    Route::post('workItems/flowNodeAssociateTasks', '\app\work_items\controller\WorkItems@flowNodeAssociateTasks'); // 需求下拉
    Route::post('workItems/regenerateStatisticsByDateRange', '\app\work_items\controller\WorkItems@regenerateStatisticsByDateRange'); // API：修正历史错误的Bug统计数据


    // 项目 类别管理
    Route::post('project/category/create', '\app\project\controller\Category@create'); // 新增
    Route::post('project/category/update', '\app\project\controller\Category@update'); // 更新
    Route::post('project/category/delete', '\app\project\controller\Category@delete'); // 删除
    Route::post('project/category/enable', '\app\project\controller\Category@enable'); // 状态启用
    Route::post('project/category/disable', '\app\project\controller\Category@disable'); // 状态禁用
    Route::post('project/category/categoryCopyTo', '\app\project\controller\Category@categoryCopyTo'); // 数据复制至项目/模板

    //工作流程图
    Route::post('iterate/workflowDiagram/create', '\app\iterate\controller\FlowProcess@create'); // 新增
    Route::post('iterate/workflowDiagram/update', '\app\iterate\controller\FlowProcess@update'); // 修改
    Route::post('iterate/workflowDiagram/delete', '\app\iterate\controller\FlowProcess@delete'); // 删除
    Route::post('iterate/workflowDiagram/copy', '\app\iterate\controller\FlowProcess@copy'); // 复制

    // 项目 工作流管理
    Route::post('project/flowStatus/create', '\app\project\controller\FlowStatus@create'); // 新增
    Route::post('project/flowStatus/update', '\app\project\controller\FlowStatus@update'); // 更新
    Route::post('project/flowStatus/delete', '\app\project\controller\FlowStatus@delete'); // 删除
    Route::get('project/flowStatus/copy', '\app\project\controller\FlowStatus@copy'); // 复制工作流

    //测试计划
    Route::post('testPlan/create', '\app\work_items\controller\TestPlan@create'); // 创建
    Route::post('testPlan/update', '\app\work_items\controller\TestPlan@update'); // 修改
    Route::post('testPlan/delete', '\app\work_items\controller\TestPlan@delete'); // 删除
    Route::post('testPlan/batchDelete', '\app\work_items\controller\TestPlan@batchDelete'); // 批量删除
    Route::post('testPlan/batchUpdate', '\app\work_items\controller\TestPlan@batchUpdate'); // 批量更新


    //测试计划 规划与执行
    Route::post('planUseCase/pageQuery', '\app\work_items\controller\PlanUseCase@pageQuery');
    Route::post('planUseCase/addBugByCategory', '\app\work_items\controller\PlanUseCase@addBugByCategory');
    Route::post('planUseCase/addBugByDemand', '\app\work_items\controller\PlanUseCase@addBugByDemand');
    Route::post('planUseCase/batchExecute', '\app\work_items\controller\PlanUseCase@batchExecute');
    Route::post('planUseCase/addBugToLatestRecord', '\app\work_items\controller\PlanUseCase@addBugToLatestRecord');
    Route::post('planUseCase/delDemand', '\app\work_items\controller\PlanUseCase@delDemand');
    Route::post('planUseCase/delCategory', '\app\work_items\controller\PlanUseCase@delCategory');
    Route::post('planUseCase/execute', '\app\work_items\controller\PlanUseCase@execute');
    Route::post('planUseCase/recordList', '\app\work_items\controller\PlanUseCase@recordList');
    Route::post('planUseCase/delCase', '\app\work_items\controller\PlanUseCase@delCase');

    //测试用例
    Route::post('testCase/create', '\app\work_items\controller\TestCase@create'); // 创建
    Route::post('testCase/update', '\app\work_items\controller\TestCase@update'); // 修改
    Route::post('testCase/delete', '\app\work_items\controller\TestCase@delete'); // 删除
    Route::post('testCase/batchDelete', '\app\work_items\controller\TestCase@batchDelete'); // 批量删除
    Route::post('testCase/batchUpdate', '\app\work_items\controller\TestCase@batchUpdate'); // 批量更新

    // 分类
    // 工作项分类管理
    Route::post('workItems/classify/create', '\app\work_items\controller\Classify@create'); // 新增
    Route::post('workItems/classify/update', '\app\work_items\controller\Classify@update'); // 更新
    Route::post('workItems/classify/updateSort', '\app\work_items\controller\Classify@updateSort'); // 更新排序
    Route::post('workItems/classify/delete', '\app\work_items\controller\Classify@delete'); // 删除

    // 迭代
    // 迭代目录
    Route::post('project/iterationCatalog/delete', '\app\project\controller\IterationCatalog@delete'); // 删除
    Route::post('project/iterationCatalog/create', '\app\project\controller\IterationCatalog@create'); // 新增
    Route::post('project/iterationCatalog/update', '\app\project\controller\IterationCatalog@update'); // 更新

    // 迭代概览
    Route::post('project/iterationCatalog/updateNode', '\app\project\controller\IterationCatalog@updateNode'); // 编辑节点
    Route::post('project/iterationCatalog/skipNode', '\app\project\controller\IterationCatalog@skipNode'); // 跳过节点

    // 项目用户
    Route::post('project/projectUser/create', '\app\project\controller\ProjectUser@create'); // 新增
    Route::post('project/projectUser/removeUser', '\app\project\controller\ProjectUser@removeUser'); // 移出项目成员

    //终端
    Route::post('client/create', '\app\client\controller\Client@create');
    Route::post('client/update', '\app\client\controller\Client@update');
    Route::post('client/delete', '\app\client\controller\Client@delete');
    Route::post('client/enable', '\app\client\controller\Client@enable');
    Route::post('client/disable', '\app\client\controller\Client@disable');

    // 枚举管理
    Route::post('enum/create', '\app\infrastructure\controller\Enum@create');
    Route::post('enum/update', '\app\infrastructure\controller\Enum@update');
    Route::post('enum/delete', '\app\infrastructure\controller\Enum@delete');

    // 统计查询 - 按场景编号组织
    Route::get('statistics/scene1/demandChangeRecords', '\app\statistics\controller\StatisticsController@getDemandChangeRecords'); // 场景1: 需求变更记录查询
    Route::get('statistics/scene2/newDemandRecords', '\app\statistics\controller\StatisticsController@getNewDemandRecords'); // 场景2: 新增需求记录查询
    Route::get('statistics/scene3-7-10/demandReviewRecords', '\app\statistics\controller\StatisticsController@getDemandReviewRecords'); // 场景3/7/10: 需求评审记录查询（相同SQL）
    Route::get('statistics/scene4/developmentDelayRecords', '\app\statistics\controller\StatisticsController@getDevelopmentDelayRecords'); // 场景4: 开发延期记录查询（带用户名解析）
    Route::get('statistics/scene5/bugStatisticsField1', '\app\statistics\controller\StatisticsController@getBugStatisticsRecords'); // 场景5-字段1: Bug统计记录查询
    Route::get('statistics/scene5/bugStatisticsField2', '\app\statistics\controller\StatisticsController@getBugStatisticsRecordsField2'); // 场景5-字段2: Bug统计记录查询
    Route::get('statistics/scene6/workHoursRejectionRecords', '\app\statistics\controller\StatisticsController@getWorkHoursRejectionRecords'); // 场景6: 工时评估打回记录查询
    Route::get('statistics/scene8/field1/iterationLevelStatistics', '\app\statistics\controller\StatisticsController@getScene8Field1Statistics'); // 场景8-字段1: 迭代级别统计查询
    Route::get('statistics/scene8/field2/iterationNodeLevelStatistics', '\app\statistics\controller\StatisticsController@getScene8Field2Statistics'); // 场景8-字段2: 迭代节点级别统计查询
    Route::get('statistics/scene9/testDelayRecords', '\app\statistics\controller\StatisticsController@getTestDelayRecords'); // 场景9: 测试延期记录查询（带用户名解析）


})
    ->middleware(AuthTokenMiddleware::class)
    ->middleware(TrimMiddleware::class)
    ->middleware(PermissionMiddleware::class)
    ->middleware(SaveUrlParams::class);


